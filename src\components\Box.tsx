import React from 'react';
import { StyleSheet, View } from 'react-native';

import { Shape } from './Shape';

type BoxProps = {
	children: null | string | JSX.Element | JSX.Element[];
};

export const Box = React.memo(({ children }: BoxProps) => {
	return (
		<View style={styles.bg}>
			<Shape direction="top" />
			<View style={styles.box}>{children}</View>
			<Shape />
		</View>
	);
});

Box.displayName = 'Box';

const styles = StyleSheet.create({
	bg: {
		flex: 1,
	},
	box: {
		flex: 1,
		backgroundColor: '#fff',
	},
});
