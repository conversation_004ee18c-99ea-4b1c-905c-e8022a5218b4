import axios from 'axios';
import { router } from 'expo-router';

import { showMessage } from '@/utils';

axios.defaults.timeout = 10000;

axios.interceptors.response.use(
	(response) => response,
	({ message, response: { data, status } }) => {
		return handleError({ message, data, status });
	},
);

type HandleError = {
	message: any;
	data: any;
	status: any;
};
export const handleError = ({ message, data, status }: HandleError) => {
	return Promise.reject({ message, data, status });
};

const handleResponse = (response: any, notification = true) => {
	if (typeof response.status !== 'undefined' && response.status === 0) {
		if (notification) {
			showMessage({
				message: response.message,
				type: 'danger',
			});
		}
		if (response.message === 'Đã hết phiên đăng nhập!') {
			router.navigate('/');
		}
		return Promise.reject(response.message);
	}
	return response;
};

export const get = async (url: string, notification: boolean = true) => {
	try {
		const response = await axios.get(url);
		if (typeof response !== 'undefined') {
			return handleResponse(response.data, notification);
		} else {
			if (notification) {
				showMessage({
					message: 'Lỗi khi kết nối máy chủ, vui lòng thử lại',
					type: 'danger',
				});
			}
		}
	} catch (e: any) {
		console.error(e);
		showMessage({
			message: e?.data.message || e.message,
			type: 'danger',
		});
	}
};

export const post = async (url: string, data: any) => {
	try {
		const response = await axios.post(url, data);
		if (typeof response !== 'undefined') {
			return handleResponse(response.data);
		} else {
			showMessage({
				message: 'Lỗi khi kết nối máy chủ, vui lòng thử lại',
				type: 'danger',
			});
		}
	} catch (e: any) {
		console.log(e);
		showMessage({
			message: e?.data.message || e.message,
			type: 'danger',
		});
	}
};
