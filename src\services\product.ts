import { get } from './api';

const images: Record<string, string> = {
	'b3570120-2baf-11ea-814b-dfa3949b1515':
		'https://touchcinema.com/storage/nuoc-suoi-17k.png',
	'cb3ab1e0-493d-11e9-98eb-cfc3bb0306da':
		'https://touchcinema.com/storage/hinh-combo/pepsi-nho-20k-lon-25k.png',
	'cf891940-493d-11e9-aec5-a38e219339f3':
		'https://touchcinema.com/storage/7up.png',
	'd587f500-493d-11e9-8a8c-8ba7d708f0f5':
		'https://touchcinema.com/storage/hinh-combo/mirinda-cam-nho-20k-lon-25k.png',
	'c12e8e80-493d-11e9-bca8-23a03dea5cd6':
		'https://touchcinema.com/medias/hinh-san-pham/bap-rang-bo-poster.jpg',
	'bb7a12a0-2c73-11ea-bcb7-654dac9542d1':
		'https://touchcinema.com/images/combo/1000058.jpg',
	'25f691c0-493f-11e9-a08e-8d9ee6b36e5f':
		'https://touchcinema.com/storage/hinh-combo/l.PNG',
	'2c88b2b0-493f-11e9-937c-413df9ff574a':
		'https://touchcinema.com/storage/hinh-combo/aaa.PNG',
	'32f4cf80-493f-11e9-9444-a3a3bb9937ff':
		'https://touchcinema.com/storage/hinh-combo/e.PNG',
	'e4f14660-2c73-11ea-a616-87a37df0c54e':
		'https://touchcinema.com/images/combo/1000058.jpg',
	'046faf60-2bb0-11ea-93c0-69475d6c0bc0':
		'https://touchcinema.com/storage/hinh-combo/aa.PNG',
	'5905c1e0-2bb0-11ea-b4f3-0342f0fd69e7':
		'https://touchcinema.com/storage/hinh-combo/s.PNG',
	'69531840-2bb0-11ea-ba96-89ffc5df3a93':
		'https://touchcinema.com/storage/hinh-combo/aaaa.PNG',
	'1b0ea620-2bf4-11ea-ad4e-f7341a12ca2e':
		'https://touchcinema.com/images/icons/icon-combo.png',
	'251b6910-2bf4-11ea-b160-f579fa13e2e7':
		'https://touchcinema.com/images/icons/icon-combo.png',
	'4acaf350-2bf4-11ea-ac61-5f1ed13f414f':
		'https://touchcinema.com/images/icons/icon-combo.png',
};

const getProducts = async () => {
	return await get('/fastfood/list');
};

const getGifts = async () => {
	const response = await get('/member/gifts');
	return {
		...response,
		data: response.data.map((item: any) => {
			if (typeof item.image == 'undefined') {
				item.image =
					typeof images[item.id] !== 'undefined'
						? images[item.id]
						: 'https://touchcinema.com/images/icons/icon-combo.png';
			}
			return item;
		}),
	};
};

export const productService = {
	getProducts,
	getGifts,
};
