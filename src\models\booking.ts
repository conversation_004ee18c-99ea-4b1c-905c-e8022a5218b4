export interface IBookingSeat {
	id: number;
	name: string;
	color: string;
	disable?: boolean;
	type?: string;
	type_seat_id: number;
	type_seat_name: string;
	status: string;
	fa?: number | boolean;
	showtime_id?: string;
	w: string;
	h: string;
	x: string;
	y: string;
	price_id?: string;
	price?: number;
}

export interface ICurrentShowtime {
	id: string;
	poster: string;
	movie: string;
	time: string;
	date: string;
	age: string;
}

export interface IBookingState {
	maps: IBookingSeat[];
	prices: IBookingPrice[];
	room: string;
	showtime: ICurrentShowtime | null;
	showtimes: IBookingShowtime[];
	seats: IBookingSeat[];
	timelines: [];
	sneakshows: [];
	errors: [];
}

export interface IBookingLabel {
	label: string;
	width: number;
	height: number;
	top: number;
}

export interface IBookingShowtime {
	id: string;
	time: string;
	date: string;
	movie: string;
}

export interface IBookingSelectedGroup {
	id: string;
	title: string;
	data: IBookingSeat[];
}

export interface IBookingPrice {
	id: string;
	price: number;
	price_for_id: number;
	price_for_name: string;
	type_seat_id: number;
}
