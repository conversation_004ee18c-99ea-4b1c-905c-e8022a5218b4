import { get, post } from './api';

type SearchType = {
	phone?: string;
	id?: string;
	identification?: string;
};

const search = async ({ phone, id, identification }: SearchType) => {
	const response = await get(
		'/member/search?phone=' +
			phone +
			'&id=' +
			id +
			'&identification=' +
			identification,
	);
	if (response.data && response.data.length > 0) {
		return response.data[0];
	}
	return false;
};

type FilterName = {
	name?: string;
};

type FilterType = SearchType & FilterName;

const filter = async ({ phone, id, identification, name }: FilterType) => {
	const response = await get(
		'/member/search?phone=' +
			phone +
			'&id=' +
			id +
			'&identification=' +
			identification +
			'&name=' +
			name,
	);
	return response;
};

const update = async (id: string, data: any) => {
	const response = await post('/member/edit', { id, data });
	return response;
};

const create = async (data: any) => {
	const response = await post('/member/add', { data });
	return response;
};

const claim = async (data: any) => {
	const response = await post('/member/gifts', data);
	return response;
};

export const memberService = {
	search,
	filter,
	update,
	create,
	claim,
};
