import React from 'react';
import { Image, StyleSheet, View } from 'react-native';

const sources: any = {
	top: require('@/assets/images/backgrounds/shape-top.png'),
	bottom: require('@/assets/images/backgrounds/shape.png'),
};

type ShapeProps = {
	direction?: string;
	height?: number;
};
export const Shape = React.memo(
	({ direction = 'bottom', height = 15 }: ShapeProps) => {
		return (
			<View style={[styles.wrap, { height }]}>
				<Image
					source={sources[direction]}
					style={styles.image}
					resizeMode="stretch"
				/>
			</View>
		);
	},
);

Shape.displayName = 'Shape';

const styles = StyleSheet.create({
	wrap: {
		width: '100%',
	},
	image: {
		width: '100%',
		height: '100%',
	},
});
