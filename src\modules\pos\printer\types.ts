export type PrinterType =
	| 'ticket'
	| 'bill'
	| 'point'
	| 'report'
	| 'product'
	| 'money';
export type PrinterDriver = 'html' | 'native';

export interface IBill {
	detail: {
		counter: string;
		time: string;
		seller: string;
		hd: string;
		member?: string;
		order_id?: string;
	};
	products: any[];
	payment: {
		total: string;
		product: string;
		input: string;
		refund: string;
	};
}

export interface ITicket {
	order_id: string;
	ticket_id: string;
	seller: string;
	create_date: string;
	times: string;
	count: any;
	movie: string;
	date: string;
	time: string;
	room: string;
	seat: string;
	type: string;
	price: string;
	stt: number;
	payment: string;
}

export interface IPoint {
	detail: {
		counter: string;
		time: string;
		seller: string;
		member: string;
	};
	products: any[];
	point: {
		used: string;
		left: string;
	};
}

export interface IReport {
	time: string;
	shift: string;
	counter: string;
	seller: string;
	sell: string;
	cancel_count: number;
	cancel: string;
	ticket_count: number;
	ticket: string;
	combo_count: number;
	combo: string;
	total: string;
	cash: string;
	card: string;
	momo: string;
	vnpay: string;
	shopeepay: string;
	grab: string;
	beamin: string;
	voucher_count: number;
	voucher: string;
}

export interface IProduct {
	time: string;
	shift: string;
	counter: string;
	seller: string;
	products: any;
	product_total: number;
}

export interface IMoney {
	time: string;
	counter: string;
	seller: string;
	data: any;
	total_quantity: number;
	total_money: string;
}
