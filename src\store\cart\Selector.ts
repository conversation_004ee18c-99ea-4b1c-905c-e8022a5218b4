import { createSelector } from '@reduxjs/toolkit';

import { RootState } from '@/store';

const cart = (state: RootState) => state.cart;

export const getCartProducts = (state: RootState) => state.cart.products;

export const getTickets = (state: RootState) => state.cart.tickets;

export const getQrcode = (state: RootState) => state.cart.qrcode;

export const getOrder = createSelector(cart, (state) => {
	return {
		order_id: state.order_id,
		bill: state.bill,
		amount: state.amount,
		products: state.products,
		tickets: state.tickets,
		payment: state.payment,
	};
});

export const getPayment = (state: RootState) => state.cart.payment;
