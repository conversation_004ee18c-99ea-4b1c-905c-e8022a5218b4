import Icon from '@expo/vector-icons/Ionicons';

import { Alert } from '@/components';
import { logout, useAppDispatch } from '@/store';

import MenuItem from './MenuItem';

const LogoutButton = () => {
	const dispatch = useAppDispatch();

	const renderIcon = ({ color, size }: { color: string; size: number }) => (
		<Icon size={size} name="exit-outline" color={color} />
	);
	const onPress = () => {
		Alert.alert('Exit', 'Bạn có chắc muốn đăng xuất?', [
			{
				text: 'Huỷ',
				style: 'cancel',
			},
			{ text: 'Đăng xuất', onPress: onLogout },
		]);
	};

	const onLogout = () => {
		dispatch(logout());
	};

	return (
		<MenuItem
			icon={renderIcon}
			onPress={onPress}
			label={'Đăng xuất'}
			isFocused={false}
		/>
	);
};

export default LogoutButton;
