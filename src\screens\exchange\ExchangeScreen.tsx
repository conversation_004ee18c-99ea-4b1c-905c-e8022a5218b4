import React, { useEffect, useState } from 'react';
import { View, StyleSheet, Text } from 'react-native';

import { primaryColor } from '@/constants';

import { Sidebar, List } from './components';

export const ExchangeScreen = () => {
	const [ready, setReady] = useState(false);

	useEffect(() => {
		requestAnimationFrame(() => {
			setReady(true);
		});
	}, []);

	return (
		<View style={styles.bg}>
			<View style={styles.colLeft}>
				<View>
					<View style={styles.header}>
						<Text style={styles.headerText}><PERSON><PERSON><PERSON> điểm</Text>
					</View>
				</View>
				<View style={styles.container}>
					<List ready={ready} />
				</View>
			</View>
			<View style={styles.colRight}>
				<Sidebar />
			</View>
		</View>
	);
};

const styles = StyleSheet.create({
	bg: {
		flex: 1,
		flexDirection: 'row',
	},
	header: {
		backgroundColor: primaryColor,
		alignItems: 'center',
		padding: 15,
	},
	headerText: {
		color: '#fff',
		fontSize: 16,
	},
	colLeft: {
		flex: 1,
	},
	container: {
		flex: 1,
		backgroundColor: 'rgba(255, 255, 255, .2)',
		overflow: 'hidden',
	},
	colRight: {
		width: 360,
		backgroundColor: '#fff',
	},
	icon: {
		zIndex: 9,
		width: 453,
		height: 180,
		marginLeft: 40,
		marginTop: 20,
	},
});
