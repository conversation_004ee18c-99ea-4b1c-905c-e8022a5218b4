import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet } from 'react-native';

export const Clock = React.memo(() => {
	const [current, setCurrent] = useState(new Date());

	useEffect(() => {
		const loop = setInterval(() => {
			requestAnimationFrame(() => {
				setCurrent(new Date());
			});
		}, 10 * 1000);
		return () => {
			clearInterval(loop);
		};
	}, []);

	const format = () => {
		let h: number = current.getHours();
		let hour: string = '';
		if (h < 10) {
			hour = '0' + h;
		} else {
			hour = h.toString();
		}
		let m = current.getMinutes();
		let min: string = '';
		if (m < 10) {
			min = '0' + m;
		} else {
			min = m.toString();
		}
		return hour + ':' + min;
	};

	return (
		<View style={styles.wrap}>
			<Text style={styles.value}>{format()}</Text>
		</View>
	);
});

Clock.displayName = 'Clock';

const styles = StyleSheet.create({
	wrap: {
		alignItems: 'center',
	},
	value: {
		fontWeight: 'bold',
		color: '#fff',
	},
});
