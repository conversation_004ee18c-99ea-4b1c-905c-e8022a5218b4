export interface IMember {
	point: number;
	point_left: number;
	point_level: number;
	member_id: string;
	name: string;
	level: string;
	can_exchange?: boolean;
	gift_diamond?: boolean | number;
	gift_vip?: boolean | number;
}

export interface IPaymentMethod {
	id: string;
	name: string;
}

export interface IGift {
	id: number;
	quantity: number;
	point: number;
	name: string;
	showtime_id?: string;
	birthday?: boolean | number;
	gift_diamond?: boolean | number;
	gift_vip?: boolean | number;
	image?: string;
	price?: number;
}

export interface ICartAmount {
	ticket: number;
	combo: number;
	total: number;
	decrease: number;
	code?: string;
}

export interface CartState {
	tickets: ICartTicket[];
	products: ICartProduct[];
	amount: ICartAmount;
	order_id: number;
	bill: string;
	money: number;
	payment_methods: IPaymentMethod[];
	payment: string;
	member: IMember | null;
	gifts: IGift[];
	usedPoint: number;
	qrcode?: string;
}

export interface CreateOrderResponse {
	tickets: ICartTicket[];
	products: ICartProduct[];
	amount: ICartAmount;
	bill: string;
	order_id: number;
	payment_methods: [];
	time?: string;
}

export interface ICartProduct {
	id?: number;
	price?: number;
	image?: string;
	name?: string;
	point?: number;
	quantity: number;
	total_amount?: number;
}

export interface ICartTicket {
	id?: number;
	order_id: number;
	movie_name_vn: string;
	movie_age?: string;
	time: string;
	date: string;
	showtime_id: string;
	price: number;
	target?: string;
	seat_name: string;
	status?: boolean;
	ticket_id: string;
	room_name: string;
	create_date: string;
	seat_id: number;
}
