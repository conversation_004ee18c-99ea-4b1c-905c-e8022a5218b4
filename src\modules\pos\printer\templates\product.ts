import { IProduct } from '../types';

const parseProduct = async (data: IProduct, printer: string) => {
	return `<Receipts>
  <Receipt name="Product" printer="${printer}" left="10">
      <Text size="12" style="bold" align="center">TOUCH CINEMA</Text>
      <Text style="bold" align="center">212 <PERSON><PERSON>, P.<PERSON>, TP.Pleiku, Gia Lai</Text>
      <Text size="8" align="center">www.touchcinema.com</Text>
      <Space height="8"/>
      <Text size="15" style="bold" align="center">DANH SÁCH HÀNG HÓA</Text>
      <Space height="10"/>
      <Table template="5,8">
          <Tr>
              <Td>
                  <Text style="bold">Ngày:</Text>
              </Td>
              <Td>
                  <Text>${data.time}</Text>
              </Td>
          </Tr>
          <Tr>
              <Td>
                  <Text style="bold">Ca bán:</Text>
              </Td>
              <Td>
                  <Text>${data.shift}</Text>
              </Td>
          </Tr>
          <Tr>
              <Td>
                  <Text style="bold">Quầy:</Text>
              </Td>
              <Td>
                  <Text>${data.counter}</Text>
              </Td>
          </Tr>
          <Tr>
              <Td>
                  <Text style="bold">Nhân viên:</Text>
              </Td>
              <Td>
                  <Text>${data.seller}</Text>
              </Td>
          </Tr>
      </Table>
      <Space height="10"/>
      <Line type="dashed"/>
      <Space height="6"/>
      <Table template="2,1">
          <Tr>
              <Td>
                  <Text align="center" size="9">Hàng hoá</Text>
              </Td>
              <Td>
                  <Text align="center" size="9">SL</Text>
              </Td>
          </Tr>
      </Table>
      <Space height="5"/>
      <Line type="dashed"/>
      <Space height="5"/>
      <Table template="2,1">
        ${data.products
					.map(
						(product: any) => `<Tr>
              <Td>
                  <Text align="center" size="9">${product[0]}</Text>
              </Td>
              <Td>
                  <Text align="center" size="9">${product[1]}</Text>
              </Td>
          </Tr>`,
					)
					.join('')}
      </Table>
      <Space height="5"/>
      <Line type="dashed"/>
      <Space height="5"/>
      <Table template="2,1">
          <Tr>
              <Td>
                  <Text align="center" size="9" style="bold">Tổng cộng</Text>
              </Td>
              <Td>
                  <Text align="center" size="9" style="bold">${
										data.product_total
									}</Text>
              </Td>
          </Tr>
      </Table>
  </Receipt>
  </Receipts>`;
};
export default parseProduct;
