import Icon from '@expo/vector-icons/Ionicons';

import { reLoad } from '@/modules/pos';

import MenuItem from './MenuItem';

const ReloadButton = () => {
	const renderIcon = ({ color, size }: { color: string; size: number }) => (
		<Icon size={size} name="reload-outline" color={color} />
	);

	const onPress = () => {
		reLoad();
	};

	return (
		<MenuItem
			icon={renderIcon}
			onPress={onPress}
			label={'Tải lại'}
			isFocused={false}
		/>
	);
};

export default ReloadButton;
