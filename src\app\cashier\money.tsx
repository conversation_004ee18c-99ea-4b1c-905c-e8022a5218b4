import React from 'react';
import { View, StyleSheet, Text, TouchableOpacity } from 'react-native';
import { useSelector } from 'react-redux';

import { Button, MoneyInput } from '@/components';
import { currencyFormat, getDateTime } from '@/utils';
import {
	getConfigs,
	getUser,
	getMoneyData,
	getMoneyTotal,
	getMoneyTotalQuantity,
	resetMoneyData,
	useAppDispatch,
} from '@/store';
import { IMoney, printTask } from '@/modules/pos';
import { router } from 'expo-router';

const MoneyScreen = () => {
	const dispatch = useAppDispatch();
	const configs = useSelector(getConfigs);
	const user = useSelector(getUser);
	const data = useSelector(getMoneyData);
	const total = useSelector(getMoneyTotal);
	const totalQuantity = useSelector(getMoneyTotalQuantity);

	const onPrint = async () => {
		try {
			const rawData: IMoney = {
				data: data.map((item) => [
					currencyFormat(item.name),
					item.quantity || 0,
					currencyFormat(item.total),
				]),
				total_money: currencyFormat(total),
				total_quantity: totalQuantity,
				counter: configs.counter,
				seller: user.name,
				time: getDateTime(),
			};
			await printTask(rawData, configs.printer, 'money');
		} catch (error) {
			console.error('Print error:', error);
		}
	};

	const onReset = () => {
		dispatch(resetMoneyData());
	};

	const onClose = () => {
		router.dismiss();
	};

	return (
		<View style={styles.container}>
			<View style={styles.wrapper}>
				<Text style={styles.title}>Kê tiền</Text>
				<View style={styles.inputWrap}>
					<View style={styles.row}>
						<View style={styles.label}>
							<Text style={styles.text}>Mệnh giá</Text>
						</View>
						<View style={styles.quantity}>
							<Text style={styles.text}>Số lượng</Text>
						</View>
						<View style={styles.total}>
							<Text style={styles.text}>Số tiền</Text>
						</View>
					</View>
					{data.map((item) => (
						<View style={styles.row} key={item.name}>
							<View style={styles.label}>
								<Text style={styles.text}>{currencyFormat(item.name)}</Text>
							</View>
							<View style={styles.quantity}>
								<MoneyInput amount={item.name} />
							</View>
							<View style={styles.total}>
								<Text style={styles.text}>{currencyFormat(item.total)}</Text>
							</View>
						</View>
					))}
					<View style={[styles.row, styles.surmary]}>
						<View style={styles.label}>
							<Text style={[styles.text, styles.bold]}>Tổng cộng</Text>
						</View>
						<View style={styles.quantity}>
							<Text style={[styles.text, styles.bold, { textAlign: 'center' }]}>
								{currencyFormat(totalQuantity)}
							</Text>
						</View>
						<View style={styles.total}>
							<Text style={[styles.text, styles.bold]}>
								{currencyFormat(total)}
							</Text>
						</View>
					</View>
				</View>
				<Button onPress={onPrint} text="In" style={styles.btn} />
				<TouchableOpacity onPress={onReset} style={styles.btnReset}>
					<Text>Đặt lại</Text>
				</TouchableOpacity>
				<TouchableOpacity onPress={onClose} style={styles.btnClose}>
					<Text style={styles.close}>Đóng</Text>
				</TouchableOpacity>
			</View>
		</View>
	);
};

const styles = StyleSheet.create({
	container: {
		flex: 1,
		alignItems: 'center',
		paddingLeft: 440,
		backgroundColor: 'rgba(0, 0, 0, .1)',
	},
	wrapper: {
		flex: 1,
		width: '100%',
		alignItems: 'center',
		justifyContent: 'center',
		backgroundColor: '#fff',
		padding: 15,
		boxShadow: '0 0 7px rgba(0, 0, 0, .3)',
	},
	title: {
		fontSize: 30,
		fontWeight: 'bold',
		paddingBottom: 10,
	},
	inputWrap: {
		flex: 1,
		width: '100%',
		maxWidth: 450,
		backgroundColor: '#fff',
		borderWidth: 1,
		borderColor: '#eee',
		padding: 15,
		borderRadius: 10,
	},
	row: {
		width: '100%',
		flexDirection: 'row',
		borderBottomWidth: 1,
		borderColor: '#eee',
		alignItems: 'center',
	},
	label: {
		width: 150,
		alignItems: 'center',
		paddingVertical: 5,
	},
	quantity: {
		width: 100,
		textAlign: 'center',
	},
	total: {
		flex: 1,
		alignItems: 'center',
	},
	text: {
		fontSize: 16,
	},
	bold: {
		fontWeight: 'bold',
	},
	surmary: {
		paddingTop: 10,
		borderBottomWidth: 0,
	},
	btn: {
		marginTop: 20,
		width: 120,
		alignItems: 'center',
		alignSelf: 'center',
	},
	btnReset: {
		marginTop: 20,
		alignSelf: 'center',
	},
	btnClose: {
		position: 'absolute',
		top: 0,
		left: 0,
		padding: 10,
	},
	close: {
		fontSize: 18,
		fontWeight: 'bold',
	},
});

export default MoneyScreen;
