import React, { memo } from 'react';
import { Text, View, ViewStyle } from 'react-native';
import isEqual from 'react-fast-compare';

import { IBookingLabel } from '@/models';

type TLabels = {
	data: IBookingLabel[] | any;
	scale: number;
};

export const Labels = memo(({ data, scale }: TLabels) => {
	return data.map((item: IBookingLabel) => (
		<SeatLabel
			label={item.label}
			width={item.width}
			height={item.height}
			top={item.top}
			key={'way-' + item.label}
			scale={scale}
		/>
	));
}, isEqual);

Labels.displayName = 'Labels';

type TSeatLabel = {
	width: number;
	height: number;
	top: number;
	label: string;
	scale?: number;
};
const SeatLabel = memo(({ width, height, top, label }: TSeatLabel) => {
	const style: ViewStyle = {
		width: width,
		height: height,
		top: top,
		position: 'absolute',
	};
	return (
		<View style={style}>
			<Text
				style={{
					fontSize: 35,
					color: '#000',
					fontWeight: 'bold',
					lineHeight: height,
					textAlign: 'center',
				}}>
				{label}
			</Text>
		</View>
	);
}, isEqual);

SeatLabel.displayName = 'SeatLabel';
