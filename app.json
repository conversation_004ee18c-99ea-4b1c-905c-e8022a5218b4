{"expo": {"name": "touchcinema-pos", "slug": "touchcinema-pos", "version": "1.0.0", "orientation": "landscape", "icon": "./assets/images/icon.png", "scheme": "touchcinema", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/icon.png", "backgroundColor": "#ffffff"}}, "web": {"bundler": "metro", "output": "single", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", "expo-font"], "experiments": {"typedRoutes": true}}}