import React, { useEffect } from 'react';
import { StyleSheet, Text, TextInput, View } from 'react-native';
import { useSelector } from 'react-redux';
import { Picker } from '@react-native-picker/picker';

import { currencyFormat } from '@/utils';
import { Button, Separator } from '@/components';
import {
	getConfigs,
	getUser,
	getShifts,
	getFromDate,
	getToDate,
	getSelectedShift,
	getReportData,
	getCanPrintProducts,
	setFromDate,
	setToDate,
	setShift,
	fetchShifts,
	generateReport,
	useAppDispatch,
} from '@/store';
import { primaryColor } from '@/constants';
import {
	printTask,
	IReport as IPrintReport,
	IProduct as IPrintProduct,
} from '@/modules/pos';

import { Detail, Results } from './components';

export const ReportScreen = () => {
	const dispatch = useAppDispatch();

	const user = useSelector(getUser);
	const configs = useSelector(getConfigs);
	const shifts = useSelector(getShifts);
	const from = useSelector(getFromDate);
	const to = useSelector(getToDate);
	const shift = useSelector(getSelectedShift);
	const data = useSelector(getReportData);
	const canPrintProducts = useSelector(getCanPrintProducts);

	useEffect(() => {
		if (shifts.length === 0) {
			dispatch(fetchShifts());
		}
	}, []); // eslint-disable-line react-hooks/exhaustive-deps

	const onReport = async () => {
		dispatch(generateReport({ from, to, shift }));
	};

	const onPrint = () => {
		if (data) {
			const report: IPrintReport = {
				time: data.detail.start + ' - ' + data.detail.end,
				counter: configs.counter,
				seller: data.detail.user,
				shift: data.detail.shift,
				total: currencyFormat(data.detail.amount.amount_total) + ' VNĐ',
				sell: currencyFormat(data.detail.amount.amount_sell) + ' VNĐ',
				cancel: currencyFormat(data.detail.amount.amount_total_cancel) + ' VNĐ',
				cancel_count: data.tickets.canceled ? data.tickets.canceled.length : 0,
				ticket: currencyFormat(data.detail.amount.amount_ticket) + ' VNĐ',
				ticket_count: data.tickets.success ? data.tickets.success.length : 0,
				combo: currencyFormat(data.detail.amount.amount_combo) + ' VNĐ',
				combo_count: data.products.success ? data.products.success.length : 0,
				cash: currencyFormat(data.detail.amount.amount_cash) + ' VNĐ',
				card: currencyFormat(data.detail.amount.card) + ' VNĐ',
				momo: currencyFormat(data.detail.amount.momo) + ' VNĐ',
				vnpay: currencyFormat(data.detail.amount.vnpay) + ' VNĐ',
				shopeepay: currencyFormat(data.detail.amount.shopeepay || 0) + ' VNĐ',
				grab: currencyFormat(data.detail.amount.grab || 0) + ' VNĐ',
				beamin: currencyFormat(data.detail.amount.beamin || 0) + ' VNĐ',
				voucher: currencyFormat(data.detail.amount.amount_voucher) + ' VNĐ',
				voucher_count: data.detail.amount.voucher_count,
			};
			printTask(report, configs.printer, 'report');
		}
	};

	const onPrintProducts = () => {
		if (data) {
			const products: IPrintProduct = {
				time: data.detail.start + ' - ' + data.detail.end,
				counter: configs.counter,
				seller: data.detail.user,
				shift: data.detail.shift,
				products: data.categories.map((item) => {
					return [item.name, item.total];
				}),
				product_total: data.total_categories,
			};
			printTask(products, configs.printer, 'product');
		}
	};

	return (
		<View style={styles.bg}>
			<View style={styles.colDetail}>
				<View style={styles.detail}>
					<View style={styles.row}>
						<View style={styles.label}>
							<Text>Từ</Text>
						</View>
						<View style={styles.value}>
							<TextInput
								value={from}
								onChangeText={(value) => dispatch(setFromDate(value))}
								style={styles.input}
							/>
						</View>
					</View>
					<View style={styles.row}>
						<View style={styles.label}>
							<Text>Đến</Text>
						</View>
						<View style={styles.value}>
							<TextInput
								value={to}
								onChangeText={(value) => dispatch(setToDate(value))}
								style={styles.input}
							/>
						</View>
					</View>
					<View style={styles.row}>
						<View style={styles.label}>
							<Text>Nhân viên</Text>
						</View>
						<View style={styles.value}>
							<TextInput value={user.name} style={styles.input} />
						</View>
					</View>
					<View style={styles.row}>
						<View style={styles.label}>
							<Text>Ca</Text>
						</View>
						<View style={styles.value}>
							<Picker
								selectedValue={shift}
								onValueChange={(value) => dispatch(setShift(value))}
								style={styles.picker}>
								{shifts.map((item) => (
									<Picker.Item
										label={item.name}
										value={item.id}
										key={item.id}
									/>
								))}
							</Picker>
						</View>
					</View>
					<Separator />
					{data && (
						<Detail
							detail={data.detail}
							total={{
								tickets: data.tickets?.success
									? data.tickets?.success.length
									: 0,
								products: data.products?.success
									? data.products?.success.length
									: 0,
							}}
						/>
					)}
				</View>
				<View style={styles.action}>
					<Button
						onPress={onReport}
						text="Thống kê"
						style={styles.btn}
						textStyle={styles.btnLabel}
					/>
					{data && (
						<>
							<Button
								onPress={onPrint}
								text="In kết ca"
								style={styles.btn}
								textStyle={styles.btnLabel}
								background={
									typeof data.detail === 'undefined' ? '#ddd' : primaryColor
								}
								disabled={typeof data.detail === 'undefined'}
							/>
							<Button
								onPress={onPrintProducts}
								text="In hàng hóa"
								style={styles.btn}
								textStyle={styles.btnLabel}
								background={!canPrintProducts ? '#ddd' : primaryColor}
								disabled={!canPrintProducts}
							/>
						</>
					)}
				</View>
			</View>
			{data && (
				<Results
					overview={data.overview}
					categories={data.categories}
					total={data.total_categories || 0}
				/>
			)}
		</View>
	);
};

const styles = StyleSheet.create({
	bg: {
		flex: 1,
		flexDirection: 'row',
		paddingHorizontal: 20,
		paddingVertical: 10,
	},
	colDetail: {
		width: 350,
		backgroundColor: '#fff',
		padding: 10,
	},
	detail: {
		flex: 1,
	},
	action: {},
	row: {
		flexDirection: 'row',
		marginVertical: 5,
		alignItems: 'center',
	},
	label: {
		width: 80,
	},
	value: {
		flex: 1,
	},
	picker: {},
	btn: {
		marginVertical: 5,
	},
	btnLabel: {
		textAlign: 'center',
		color: '#fff',
	},
	input: {},
});
