import React, { useEffect, useState } from 'react';
import {
	Text,
	View,
	TextInput,
	TouchableOpacity,
	StyleSheet,
} from 'react-native';
import Icon from '@expo/vector-icons/Ionicons';
import { useSelector } from 'react-redux';
import { Picker } from '@react-native-picker/picker';
import axios from 'axios';
import { Redirect } from 'expo-router';

import { primaryColor } from '@/constants';
import {
	Button,
	Modal,
	Toast,
	showMessage,
	ImageBackground,
} from '@/components';
import {
	useAppDispatch,
	getConfigs,
	getUser,
	loginAction,
	setConfigs,
} from '@/store';

type ServerItem = {
	name: string;
	api: string;
	id: string;
};

const WelcomeScreen = () => {
	const dispatch = useAppDispatch();

	const configs = useSelector(getConfigs);
	const user = useSelector(getUser);

	const [email, setEmail] = useState<string>('');
	const [pass, setPass] = useState<string>('');

	const [modal, setModal] = useState(false);
	const [server, setServer] = useState(configs.server);
	const [servers, setServers] = useState([]);

	useEffect(() => {
		getServers();
	}, []);

	const getServers = async () => {
		try {
			const { data } = await axios.get('https://pos.touchcinema.com/servers/');
			setServers(data.data);
		} catch (e) {
			console.log(e);
		}
	};

	const saveSetting = () => {
		dispatch(
			setConfigs({
				...configs,
				server: server,
			}),
		);
		setModal(false);
		setTimeout(() => {
			showMessage({
				type: 'success',
				message: 'Lưu thành công',
			});
		}, 100);
	};

	const onLogin = () => {
		dispatch(
			loginAction({
				email: email,
				password: pass,
			}),
		);
	};

	const closeModal = () => {
		setModal(false);
	};

	const openModal = () => {
		setModal(true);
	};

	const onChangeServer = (value: string): void => {
		setServer(value);
	};

	if (user.name) {
		return <Redirect href="/cashier" />;
	}

	return (
		<ImageBackground
			source={require('@/assets/images/bg.jpg')}
			style={styles.bg}>
			<Toast visibilityTime={2500} topOffset={20} />
			<View style={styles.wrap}>
				<TextInput
					value={email}
					onChangeText={setEmail}
					placeholder="Tên đăng nhập"
					style={styles.input}
					onSubmitEditing={onLogin}
				/>
				<TextInput
					value={pass}
					onChangeText={setPass}
					placeholder="Mật khẩu"
					secureTextEntry
					style={styles.input}
					onSubmitEditing={onLogin}
				/>
				<Button text="Đăng nhập" style={styles.btn} onPress={onLogin} />
			</View>
			<Modal visible={modal} style={styles.modal} onRequestClose={closeModal}>
				<View style={styles.modalBody}>
					<Text>Chọn máy chủ</Text>
					<Picker
						selectedValue={server}
						onValueChange={onChangeServer}
						style={styles.picker}>
						{servers.map((item: ServerItem) => (
							<Picker.Item label={item.name} value={item.api} key={item.id} />
						))}
					</Picker>
					<Button onPress={saveSetting} text="Lưu" style={styles.btnSave} />
				</View>
			</Modal>
			<TouchableOpacity style={styles.settingIcon} onPress={openModal}>
				<Icon name="settings-outline" size={40} color={primaryColor} />
			</TouchableOpacity>
		</ImageBackground>
	);
};

const styles = StyleSheet.create({
	bg: {
		flex: 1,
	},
	wrap: {
		backgroundColor: 'rgba(0, 0, 0, 0.7)',
		width: 400,
		alignSelf: 'flex-end',
		flex: 1,
		padding: 20,
		paddingTop: '20%',
	},
	input: {
		margin: 10,
		paddingHorizontal: 20,
		paddingVertical: 10,
		fontSize: 16,
		borderRadius: 10,
		backgroundColor: '#fff',
	},
	btn: {
		alignItems: 'center',
		borderRadius: 10,
		marginTop: 30,
	},
	btnSave: {
		width: 150,
		borderRadius: 0,
		alignSelf: 'center',
		alignItems: 'center',
	},
	settingIcon: {
		position: 'absolute',
		top: 20,
		right: 20,
	},
	picker: {
		marginVertical: 20,
		padding: 8,
	},
	modal: {},
	modalBody: {
		backgroundColor: '#fff',
		padding: 40,
		minWidth: 500,
		minHeight: 200,
	},
});

export default WelcomeScreen;
