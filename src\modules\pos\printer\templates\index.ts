import parseBill from './bill';
import parseTickets from './ticket';
import parseMoney from './money';
import parseReport from './report';
import parseProduct from './product';
import parsePoint from './point';

const parse = async (
	template: 'ticket' | 'bill' | 'point' | 'report' | 'product' | 'money',
	data: any,
	printer: string,
): Promise<string> => {
	let xml = '';
	switch (template) {
		case 'ticket':
			xml = await parseTickets(data, printer);
			break;
		case 'bill':
			xml = await parseBill(data, printer);
			break;
		case 'point':
			xml = await parsePoint(data, printer);
			break;
		case 'report':
			xml = await parseReport(data, printer);
			break;
		case 'product':
			xml = await parseProduct(data, printer);
			break;
		case 'money':
			xml = await parseMoney(data, printer);
			break;
	}
	return xml;
};

export default parse;
