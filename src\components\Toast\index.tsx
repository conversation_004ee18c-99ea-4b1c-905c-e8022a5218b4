import React from 'react';
import { StyleSheet } from 'react-native';
import RNToast, { BaseToast } from 'react-native-toast-message';

export const toastConfig = {
	success: (props: any) => (
		<BaseToast
			{...props}
			style={styles.success}
			contentContainerStyle={styles.content}
			text1Style={styles.text}
			text1NumberOfLines={2}
		/>
	),
	error: (props: any) => (
		<BaseToast
			{...props}
			style={styles.error}
			contentContainerStyle={styles.content}
			text1Style={styles.text}
			text1NumberOfLines={2}
		/>
	),
	danger: (props: any) => (
		<BaseToast
			{...props}
			style={styles.error}
			contentContainerStyle={styles.content}
			text1Style={styles.text}
			text1NumberOfLines={2}
		/>
	),
	info: (props: any) => (
		<BaseToast
			{...props}
			style={styles.info}
			contentContainerStyle={styles.content}
			text1Style={styles.text}
			text1NumberOfLines={2}
		/>
	),
	warning: (props: any) => (
		<BaseToast
			{...props}
			style={styles.warning}
			contentContainerStyle={styles.content}
			text1Style={styles.text}
			text1NumberOfLines={2}
		/>
	),
};

type showMessageProps = {
	type?: string;
	message: string;
	bottom?: number;
	duration?: number;
	onPress?(): void;
};

export const showMessage = ({
	type,
	message,
	bottom,
	duration,
	onPress,
}: showMessageProps) => {
	const onPressHandler =
		typeof onPress !== 'undefined'
			? onPress
			: () => {
					RNToast.hide();
				};
	RNToast.show({
		type,
		text1: message,
		bottomOffset: bottom,
		position: bottom ? 'bottom' : 'top',
		visibilityTime: duration,
		onPress: onPressHandler,
	});
};

export const Toast = ({ visibilityTime = 2500, topOffset = 20 }) => {
	return (
		<RNToast
			config={toastConfig}
			visibilityTime={visibilityTime}
			topOffset={topOffset}
		/>
	);
};

const styles = StyleSheet.create({
	content: {},
	success: {
		borderColor: '#42a85f',
		borderLeftColor: '#42a85f',
		backgroundColor: '#68cd86',
	},
	error: {
		borderColor: '#b82e24',
		borderLeftColor: '#b82e24',
		backgroundColor: '#e54d42',
	},
	info: {
		borderColor: '#44a0ba',
		borderLeftColor: '#44a0ba',
		backgroundColor: '#1ab2dd',
	},
	warning: {
		borderColor: '#f37405',
		borderLeftColor: '#f37405',
		backgroundColor: '#f98118',
	},
	text: {
		fontSize: 15,
		color: '#fff',
	},
});
