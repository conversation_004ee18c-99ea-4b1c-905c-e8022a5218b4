export interface ITabItem {
	date: string;
	title: string;
	day: string;
	month: string;
}

export type TabsProps = {
	active: string;
	onPress(date: string): void | null | Promise<void>;
};

export type TabProps = {
	active: boolean;
	data: ITabItem;
	onPress(date: string): void | null | Promise<void>;
};

export type RoomProps = {
	first: boolean;
	cellWidth: number;
	data: Room;
};

export type Room = {
	room: string;
	data: [];
};

export interface IShowtime {
	to: string;
	from: string;
	color: string;
	movie: string;
	movie_age?: string;
	total: number;
	booked: number;
}

export type ShowtimeProps = {
	showtime: IShowtime;
	cellWidth: number;
};
