import React, { useMemo, useCallback, useEffect } from 'react';
import { StyleSheet, Text, View } from 'react-native';
import { useSelector } from 'react-redux';
import { router, usePathname } from 'expo-router';

import {
	Button,
	Separator,
	MemberButton,
	Alert,
	HoldButton,
} from '@/components';
import {
	useAppDispatch,
	setSeats,
	getSelected,
	getPrices,
	CreateOrderParams,
	createOrder,
	setMember,
	getProducts,
	setProducts,
} from '@/store';
import { currencyFormat, isCustomer, showMessage } from '@/utils';
import { bookingService, sendToCustomer } from '@/services';
import { useShortcut } from '@/hooks';
import type { IBookingPrice, IBookingSeat } from '@/models';
import { useThrottledCallback } from 'use-debounce';

type Props = {
	ticket?: boolean;
	children?: string | JSX.Element | JSX.Element[];
};

export const BoxAction = React.memo(({ ticket, children }: Props) => {
	const dispatch = useAppDispatch();

	const pathname = usePathname();

	const prices = useSelector(getPrices);
	const products = useSelector(getProducts);
	const seats = useSelector(getSelected);

	const isModal = pathname.includes('booking-products');

	const isCustomerScreen = isCustomer();

	const toCustomer = useThrottledCallback(
		(seats, products) => {
			sendToCustomer({
				action: 'setProducts',
				payload: {
					seats,
					products,
				},
			});
		},
		500,
		{
			leading: false,
		},
	);

	useEffect(() => {
		if (!isCustomerScreen) {
			toCustomer(seats, products);
		}
	}, [seats, products, isCustomerScreen]); // eslint-disable-line react-hooks/exhaustive-deps

	const totalProduct = useMemo(() => {
		if (products.length > 0) {
			return products
				.map((item) => (item.price ? item.price : 0) * item.quantity)
				.reduce((prev: number, next: number) => prev + next);
		}
		return 0;
	}, [products]);

	const totalTicket = useMemo(() => {
		if (seats.length > 0) {
			return seats
				.map((seat: IBookingSeat) => {
					return (
						prices.find(
							(item: IBookingPrice) =>
								item.type_seat_id === seat.type_seat_id &&
								item.id === seat.price_id,
						)?.price || 0
					);
				})
				.reduce((prev: number, next: number) => prev + next);
		}
		return 0;
	}, [seats, prices]);

	const total = useMemo(() => {
		return totalProduct + totalTicket;
	}, [totalProduct, totalTicket]);

	const reset = useCallback(() => {
		bookingService.reset();
		dispatch(setProducts([]));
		dispatch(setSeats([]));
	}, []); //	eslint-disable-line react-hooks/exhaustive-deps

	const handleCheckout = async () => {
		if (seats.length === 0 && products.length === 0) {
			showMessage({
				message: 'Vui lòng chọn trước khi thanh toán',
				type: 'danger',
			});
			return;
		}

		const formData: CreateOrderParams = {
			tickets: [],
			combo: [],
			previos_screen: pathname.replace(
				'/cashier/booking-products',
				'/cashier/booking',
			),
		};
		seats.forEach((seat: IBookingSeat) =>
			formData.tickets.push({
				seat_id: seat.id,
				price_id: seat.price_id,
			}),
		);
		products.forEach((item) =>
			formData.combo.push({
				combo_id: item.id,
				quantity: item.quantity,
			}),
		);
		dispatch(createOrder(formData));
	};

	const onCheckout = useThrottledCallback(handleCheckout, 2000, {
		trailing: false,
	});

	// Register F1 shortcut for checkout
	useShortcut('F1', onCheckout, !isCustomerScreen);

	const onCancel = useCallback(() => {
		if (totalTicket > 0) {
			Alert.alert('Quay lại', 'Đang có ghế đang chọn, bạn có muốn giữ lại', [
				{
					text: 'Không',
					onPress: backWithReset,
				},
				{
					text: 'Giữ ghế',
					onPress: router.back,
				},
			]);
		} else {
			dispatch(setMember(null));
			router.dismiss();
		}
	}, [totalTicket]); // eslint-disable-line react-hooks/exhaustive-deps

	const backWithReset = () => {
		bookingService.reset();
		dispatch(setSeats([]));
		dispatch(setMember(null));
		router.dismiss();
	};

	const toProducts = useCallback(() => {
		router.navigate({
			pathname: '/cashier/booking-products',
		});
	}, []);

	const toBooking = useCallback(() => {
		router.back();
	}, []);

	return (
		<View>
			<Separator />
			{ticket && (
				<>
					{children}
					<View style={styles.attr}>
						<Text style={styles.text}>Tiền vé:</Text>
						<Text style={styles.value}>{currencyFormat(totalTicket)} đ</Text>
					</View>
					<View style={styles.attr}>
						<Text style={styles.text}>Tiền bắp nước:</Text>
						<Text style={styles.value}>{currencyFormat(totalProduct)} đ</Text>
					</View>
				</>
			)}
			<View style={styles.attr}>
				<Text style={styles.text}>Tổng cộng:</Text>
				<Text style={styles.value}> {currencyFormat(total)} đ</Text>
			</View>
			{!isCustomerScreen && (
				<View style={styles.action}>
					{ticket && (
						<>
							<View style={styles.row}>
								<Button
									style={styles.btn}
									onPress={onCancel}
									text="Chọn phim"
									textStyle={styles.btnText}
								/>
								<Button
									onPress={reset}
									style={styles.btn}
									text="Chọn lại"
									textStyle={styles.btnText}
								/>
							</View>
							<View style={styles.row}>
								<Button
									style={styles.btn}
									onPress={toProducts}
									text="Bắp nước"
									textStyle={styles.btnText}
								/>
								<MemberButton
									style={styles.btnFix}
									text="KHTT"
									textStyle={styles.btnText}
								/>
							</View>
						</>
					)}
					{isModal ? (
						<View style={styles.row}>
							<Button
								onPress={toBooking}
								style={styles.btn}
								text="Chọn ghế"
								textStyle={styles.btnText}
							/>
							<Button
								onPress={onCheckout}
								style={styles.btn}
								text="Thanh toán (F1)"
								textStyle={styles.btnText}
							/>
						</View>
					) : (
						<View style={styles.row}>
							{ticket && (
								<HoldButton style={styles.btn} textStyle={styles.btnText} />
							)}
							<Button
								onPress={onCheckout}
								style={styles.btn}
								text="Thanh toán (F1)"
								textStyle={styles.btnText}
							/>
						</View>
					)}
				</View>
			)}
			{isCustomerScreen && <View style={styles.bottom} />}
		</View>
	);
});

BoxAction.displayName = 'BoxAction';

const styles = StyleSheet.create({
	action: {
		margin: 3,
	},
	btn: {
		flex: 1,
		margin: 3,
		marginHorizontal: 3,
		paddingHorizontal: 4,
		justifyContent: 'center',
	},
	btnFix: {
		flex: 1,
		margin: 3,
		marginHorizontal: 3,
		paddingHorizontal: 4,
		justifyContent: 'center',
		height: 40,
	},
	btnText: {
		color: '#fff',
		textAlign: 'center',
		fontSize: 14,
		fontWeight: 'bold',
		textTransform: 'uppercase',
	},
	row: {
		flexDirection: 'row',
	},
	attr: {
		marginHorizontal: 10,
		flexDirection: 'row',
		justifyContent: 'space-between',
	},
	text: {
		color: '#000',
		fontSize: 16,
	},
	value: {
		fontWeight: 'bold',
		fontSize: 16,
	},
	bottom: {
		height: 8,
	},
});
