import {
	IBill,
	IMoney,
	IProduct,
	IReport,
	IPoint,
	ITicket,
	PrinterDriver,
	PrinterType,
} from './types';

import parse from './templates';

export const printTask = async (
	data: IBill | ITicket[] | IReport | IProduct | IMoney | IPoint,
	printer: string,
	template: PrinterType,
	driverName: PrinterDriver = 'native',
) => {
	const raw: string = await parse(template, data, printer);
	await toPrinter(raw, driverName, template);
};

export const toPrinter = async (
	data: string,
	driver: PrinterDriver = 'native',
	name: PrinterType = 'ticket',
): Promise<void> => {
	console.log(data, name, driver);
};

export const getPrinters = async (): Promise<string[]> => {
	return [];
};

export const writeFile = async (file: string, content: string) => {
	console.log(file, content);
};
