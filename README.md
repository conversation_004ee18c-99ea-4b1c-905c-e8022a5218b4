# TouchCinema POS 👋

Ứng dụng phát triển bằng [Tauri](https://tauri.app) và giao diện sử dụng [Expo](https://expo.dev).

## Get started

1. Cài đặt môi trường phát triển

- <PERSON><PERSON><PERSON> cầu máy đã cài **nodejs**, **pnpm**, **rust**
- C<PERSON>i theo hướng dẫn của tauri: [Prerequisites](https://tauri.app/start/prerequisites/)
- Cài đặt các packages bằng pnpm:
  ```bash
   pnpm install
  ```

2. Chạy ứng dụng

   ```bash
    pnpm run desktop:dev
   ```

## Phát hành ứng dụng

1.  Cài đặt key ký ứng dụng để update

- Tạo file .env ở root:

```
TAURI_SIGNING_PRIVATE_KEY=xxxx
TAURI_SIGNING_PRIVATE_KEY_PASSWORD=xxxx
```

key liên hệ <EMAIL> để lấy

```bash
  pnpm run desktop:build
```

2.  Cập nhật phần mềm

- cài đặt [tw-updater-cli](https://npmjs.com/package/tw-updater-cli)
- Đăng nhập và chạy lệnh cập nhật

```bash
tw-updater login
pnpm run desktop:update -n "fix bugs"
```

3. More info: [Tauri Updater](https://tauri.app/plugin/updater/)

## In hoá đơn

1. Sửa template in vé, hoá đơn ở **src/utils/printer/templates**
2. Template viết theo cấu trúc **XML**, hỗ trợ các components: **Text, Image, Table, Qrcode, Line, Space**

## Learn more

To learn more about developing your project with Expo, look at the following resources:

- [Tauri Guides](https://tauri.app/start/): Whether you're new to Tauri development, want to integrate Tauri into an existing project, are ready to get your app into the hands of users, or want to learn how to use a common Tauri feature.
- [Expo documentation](https://docs.expo.dev/): Learn fundamentals, or go into advanced topics with our [guides](https://docs.expo.dev/guides).
- [Learn Expo tutorial](https://docs.expo.dev/tutorial/introduction/): Follow a step-by-step tutorial where you'll create a project that runs on Android, iOS, and the web.
