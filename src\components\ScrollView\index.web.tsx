import React from 'react';
import {
	ScrollView as NativeScrollView,
	ScrollViewProps as NativeScrollViewProps,
	Platform,
} from 'react-native';
import ScrollContainer from 'react-indiana-drag-scroll';

interface WebScrollViewProps {
	showScroller?: boolean;
}

type ScrollViewProps = NativeScrollViewProps & WebScrollViewProps;

export const ScrollView = React.memo((props: ScrollViewProps) => {
	if (Platform.OS === 'web') {
		let className = 'scroll-container';

		if (props.showScroller) {
			className += ' scroll';
		}
		const horizontal =
			typeof props.horizontal !== 'undefined'
				? props.horizontal === true
					? true
					: false
				: false;

		return (
			<ScrollContainer
				className={className}
				hideScrollbars={false}
				vertical={!horizontal}
				horizontal={horizontal}>
				{props.children}
			</ScrollContainer>
		);
	} else {
		return <NativeScrollView {...props} />;
	}
});

ScrollView.displayName = 'ScrollView';
