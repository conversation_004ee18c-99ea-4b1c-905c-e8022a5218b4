import React, { useImperativeHandle, useRef } from 'react';
import {
	FlatList as NativeFlatList,
	FlatListProps as NativeFlatListProps,
} from 'react-native';
import ScrollContainer from 'react-indiana-drag-scroll';

interface WebFlatListProps {
	listKey?: string | Function;
	ref?: any;
}

type ScrollOffset = {
	offset: number;
	animated?: boolean;
};

interface Ref {
	scrollToEnd(): void;
	scrollToOffset(params: ScrollOffset): void;
}

export type FlatListProps = NativeFlatListProps<any> & WebFlatListProps;

export const FlatList = React.forwardRef<Ref, FlatListProps>((props, ref) => {
	const scroller = useRef<any>(null);

	useImperativeHandle(ref, () => ({
		scrollToEnd() {
			if (scroller.current) {
				scroller.current.scrollTop = scroller?.current.clientHeight;
			}
		},
		scrollToOffset(params: ScrollOffset) {
			if (scroller.current) {
				scroller.current.scrollTop = params.offset;
			}
		},
	}));

	let className = 'scroll-container';

	if (
		props.showsHorizontalScrollIndicator ||
		props.showsVerticalScrollIndicator
	) {
		className += ' scroll';
	}

	const horizontal =
		typeof props.horizontal !== 'undefined'
			? props.horizontal === true
				? true
				: false
			: false;

	if (horizontal) {
		className += ' scroll-horizontal';
	}

	return (
		<ScrollContainer
			className={className}
			hideScrollbars={false}
			vertical={!horizontal}
			horizontal={horizontal}
			innerRef={scroller}>
			<NativeFlatList {...props} />
		</ScrollContainer>
	);
});

FlatList.displayName = 'FlatList';

export type FlatListRef<T> = NativeFlatList<T>;
