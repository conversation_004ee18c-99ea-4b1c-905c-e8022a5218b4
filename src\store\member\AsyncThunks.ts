import { createAsyncThunk } from '@reduxjs/toolkit';

import { memberService } from '@/services';
import { setLoading } from '@/store';

export type SearchMemberData = {
	id: string;
	name: string;
	phone: string;
	identification: string;
};
export const fectchData = createAsyncThunk(
	'member/fectchData',
	async (args: SearchMemberData, thunkApi) => {
		thunkApi.dispatch(setLoading(true));
		try {
			const response = await memberService.filter(args);
			thunkApi.dispatch(setLoading(false));
			return response;
		} catch (e) {
			console.log(e);
		}
		thunkApi.dispatch(setLoading(false));
		return thunkApi.rejectWithValue('Error');
	},
);
