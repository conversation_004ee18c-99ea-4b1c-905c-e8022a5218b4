import React, { useContext } from 'react';
import { Modal, Text, TouchableOpacity, View } from 'react-native';

import { ActionType, AlertButton } from './types';
import { AlertContext } from './AlertProvider';
import styles from './styles';

export const ModalAlert = React.memo(() => {
	const { state, dispatch } = useContext(AlertContext);

	const onClose = () => {
		dispatch({
			type: ActionType.HIDE,
		});
	};

	const onPress = (callback: Function): void => {
		dispatch({
			type: ActionType.HIDE,
		});
		callback();
	};

	return (
		<Modal
			animationType="none"
			transparent={true}
			visible={state.visible}
			onRequestClose={onClose}>
			{state.visible && (
				<View style={styles.overlay}>
					<View style={styles.modal}>
						<View style={styles.content}>
							<Text style={styles.message}>{state.message}</Text>
						</View>
						<View style={styles.buttons}>
							{state.buttons.map((btn: AlertButton, index: number) => (
								<View key={'btn-' + index} style={styles.button}>
									<TouchableOpacity
										onPress={
											btn.onPress ? () => onPress(btn.onPress) : onClose
										}>
										<Text style={styles.buttonText}>{btn.text}</Text>
									</TouchableOpacity>
								</View>
							))}
						</View>
					</View>
				</View>
			)}
		</Modal>
	);
});

ModalAlert.displayName = 'ModalAlert';
