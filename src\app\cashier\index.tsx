import React from 'react';
import { Text, StyleSheet, TouchableOpacity, View } from 'react-native';
import Icon from '@expo/vector-icons/Ionicons';
import { useSelector } from 'react-redux';
import { router } from 'expo-router';

import { getUser, setMenu, useAppDispatch } from '@/store';

const HomeScreen = () => {
	const dispatch = useAppDispatch();
	const user = useSelector(getUser);

	const toCinema = () => {
		dispatch(setMenu('/cashier/tickets'));
		router.replace('/cashier/tickets');
	};

	const toProduct = () => {
		dispatch(setMenu('/cashier/products'));
		router.replace('/cashier/products');
	};

	const toPrint = () => {
		dispatch(setMenu('/cashier/print'));
		router.replace('/cashier/print');
	};

	const toMember = () => {
		dispatch(setMenu('/cashier/members'));
		router.replace('/cashier/members');
	};

	const toReport = () => {
		dispatch(setMenu('/cashier/reports'));
		router.replace('/cashier/reports');
	};

	const toTimelines = () => {
		dispatch(setMenu('/cashier/timelines'));
		router.replace('/cashier/timelines');
	};

	return (
		<View style={styles.bg}>
			<View style={styles.row}>
				{user?.permission.indexOf('can_ticket') >= 0 && (
					<TouchableOpacity style={styles.box} onPress={toCinema}>
						<Icon name="images-outline" size={70} />
						<Text style={styles.label}>Bán vé</Text>
					</TouchableOpacity>
				)}
				{user?.permission.indexOf('can_fastfood') >= 0 && (
					<TouchableOpacity style={styles.box} onPress={toProduct}>
						<Icon name="fast-food-outline" size={70} />
						<Text style={styles.label}>Bắp nước</Text>
					</TouchableOpacity>
				)}
				<TouchableOpacity style={styles.box} onPress={toPrint}>
					<Icon name="print-outline" size={70} />
					<Text style={styles.label}>In vé</Text>
				</TouchableOpacity>
			</View>
			<View style={styles.row}>
				{user?.permission?.indexOf('can_member') >= 0 && (
					<TouchableOpacity style={styles.box} onPress={toMember}>
						<Icon name="people-outline" size={70} />
						<Text style={styles.label}>Thành viên</Text>
					</TouchableOpacity>
				)}
				<TouchableOpacity style={styles.box} onPress={toTimelines}>
					<Icon name="calendar-outline" size={70} />
					<Text style={styles.label}>Lịch chiếu</Text>
				</TouchableOpacity>
				<TouchableOpacity style={styles.box} onPress={toReport}>
					<Icon name="newspaper-outline" size={70} />
					<Text style={styles.label}>Kết ca</Text>
				</TouchableOpacity>
			</View>
		</View>
	);
};

const styles = StyleSheet.create({
	bg: {
		flex: 1,
		paddingHorizontal: 15,
		paddingVertical: 100,
	},
	row: {
		flexDirection: 'row',
		flex: 1,
	},
	box: {
		backgroundColor: '#fff',
		flex: 1,
		margin: 15,
		alignItems: 'center',
		justifyContent: 'center',
		elevation: 2,
	},
	label: {
		fontSize: 20,
		fontWeight: 'bold',
	},
});

export default HomeScreen;
