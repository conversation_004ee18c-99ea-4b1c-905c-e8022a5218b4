export const getVersion = async (): Promise<string> => {
	return '';
};

export const checkUpdate = async (): Promise<boolean> => {
	return false;
};

export const requestUpdate = async (): Promise<void> => {};

export const openCashDrawer = async (): Promise<void> => {};

export const toggleFullScreen = async (): Promise<void> => {};

export const exitApp = async (): Promise<void> => {};

export const reLoad = async (): Promise<void> => {};
