import React, { memo, useEffect, useMemo } from 'react';
import { View, StyleSheet, ActivityIndicator } from 'react-native';
import { useSelector } from 'react-redux';

import { fetchGifts, getGifts, getMember, useAppDispatch } from '@/store';
import { FlatList, ProductItem } from '@/components';
import { IGift } from '@/models';
import { primaryColor } from '@/constants';

export const List = memo(({ ready }: { ready: boolean }) => {
	const dispatch = useAppDispatch();
	const data = useSelector(getGifts);
	const member = useSelector(getMember);

	useEffect(() => {
		if (data.length === 0) {
			dispatch(fetchGifts(false));
		}
	}, []); // eslint-disable-line react-hooks/exhaustive-deps

	const renderItem = ({ item }: { item: IGift }) => (
		<ProductItem data={item} type="point" />
	);

	const gifts = useMemo(() => {
		return data.filter((item: IGift) => {
			if (member) {
				if (item.birthday === 1) {
					if (member.can_exchange) {
						console.log(item.name.search('SN ' + member.level));
						if (item.name.search('SN ' + member.level) >= 0) {
							return true;
						}
					}
					return false;
				}
				if (
					(item.gift_diamond === 1 && member.gift_diamond !== 1) ||
					(item.gift_vip === 1 && member.gift_vip !== 1)
				) {
					return false;
				}
			}
			return true;
		});
	}, [data, member]);

	return (
		<View style={styles.wrap}>
			{!ready && (
				<ActivityIndicator
					size="large"
					color={primaryColor}
					style={styles.loading}
				/>
			)}
			{ready && data.length > 0 && (
				<FlatList
					keyExtractor={(item) => item.id}
					renderItem={renderItem}
					data={gifts}
					numColumns={4}
				/>
			)}
		</View>
	);
});
List.displayName = 'ListProduct';

const styles = StyleSheet.create({
	wrap: {
		flex: 1,
		paddingHorizontal: 5,
	},
	loading: {
		marginTop: '20%',
	},
});
