import React from 'react';
import { TextInputProps } from 'react-native';

interface Props extends Omit<TextInputProps, 'style'> {
	style: any;
}

export const NumberInput = React.memo((props: Props) => {
	const onChange = (event: React.ChangeEvent<HTMLInputElement>) => {
		if (props.onChangeText) {
			props.onChangeText(event.target.value);
		}
	};

	return (
		<input
			type="number"
			value={props.value}
			onChange={onChange}
			style={props.style}
			className="custom-number-input"
		/>
	);
});

NumberInput.displayName = 'NumberInput';
