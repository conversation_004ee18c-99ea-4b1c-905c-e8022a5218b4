import { get, post } from './api';

type Movie = {
	id: string | number;
	name_vn: string;
};

const getMovies = async () => {
	const response = await get('/cinema/movies');
	return response.data.map((item: Movie) => {
		return {
			id: item.id,
			name: item.name_vn,
		};
	});
};

const getShowtimes = async (movieId: string) => {
	const response = await get('/cinema/showtimes?movie_id=' + movieId);
	return response;
};

const searchOrder = async (orderId: string) => {
	const response = await get('/cinema/search?order_id=' + orderId);
	return {
		bill: response.bill,
		products: response.combo,
		tickets: response.data,
		print: response.print,
	};
};

const searchData = async (data: any, type = 'ticket') => {
	const response = await get(
		'/cinema/' +
			(type === 'ticket' ? 'search' : 'hold') +
			'?' +
			new URLSearchParams(data).toString(),
	);
	return response.data;
};

const printTicket = async (ticketId: string | number, type = 'ticket') => {
	const response = await post(
		'/cinema/' + (type === 'ticket' ? 'ticket' : 'print_hold'),
		{
			id: ticketId,
		},
	);
	return response.data;
};

const printBill = async (bill: string) => {
	const response = await post('/cinema/print', {
		bill,
	});
	return response;
};

export const printService = {
	getMovies,
	getShowtimes,
	searchOrder,
	searchData,
	printTicket,
	printBill,
};
