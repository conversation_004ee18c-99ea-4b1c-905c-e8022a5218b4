import React, { useMemo } from 'react';
import { StyleSheet, Text, View } from 'react-native';
import { useSelector } from 'react-redux';

import { currencyFormat, isCustomer } from '@/utils';
import { Separator } from '@/components';
import { getAmount, getMoney } from '@/store';

import MoneyKeyboard from './MoneyKeyboard';

export const Detail = React.memo(() => {
	const amount = useSelector(getAmount);
	const money = useSelector(getMoney);

	const refund = useMemo(() => {
		let value = money - amount.total;
		if (value > 0) {
			return value;
		}
		return 0;
	}, [money, amount.total]);

	const totalAmount = useMemo(() => {
		return amount.decrease && amount.decrease > 0
			? amount.total - amount.decrease
			: amount.total;
	}, [amount.total, amount.decrease]);

	const isCustomerScreen = isCustomer();

	return (
		<View style={styles.bg}>
			<View style={styles.content}>
				<View style={styles.attr}>
					<Text style={styles.price}>Tiền vé:</Text>
					<Text style={styles.priceValue}>
						{currencyFormat(amount.ticket || 0)} đ
					</Text>
				</View>
				<View style={styles.attr}>
					<Text style={styles.price}>Tiền bắp nước:</Text>
					<Text style={styles.priceValue}>
						{currencyFormat(amount.combo || 0)} đ
					</Text>
				</View>
				{typeof amount.decrease !== 'undefined' && amount.decrease > 0 && (
					<>
						<View style={styles.attr}>
							<Text style={styles.price}>Voucher:</Text>
						</View>
						<View style={styles.attr}>
							{amount.code && <Text style={styles.voucher}>{amount.code}</Text>}
							<Text style={styles.priceValue}>
								-{currencyFormat(amount.decrease)} đ
							</Text>
						</View>
					</>
				)}
				<Separator />
				<View style={styles.attr}>
					<Text style={styles.price}>Tổng thanh toán:</Text>
					<Text style={styles.priceValue}>
						{currencyFormat(totalAmount || 0)} đ
					</Text>
				</View>
				<View style={styles.attr}>
					<Text style={styles.price}>Tiền khách trả:</Text>
					<Text style={styles.priceValue}>{currencyFormat(money || 0)} đ</Text>
				</View>
				<View style={styles.attr}>
					<Text style={styles.price}>Tiền trả lại:</Text>
					<Text style={styles.priceValue}>{currencyFormat(refund || 0)} đ</Text>
				</View>
				{!isCustomerScreen && (
					<Text style={styles.refundText}>Nhập tiền khách trả</Text>
				)}
			</View>
			{!isCustomerScreen && <MoneyKeyboard />}
		</View>
	);
});

Detail.displayName = 'CheckoutDetail';

const styles = StyleSheet.create({
	bg: {
		backgroundColor: '#fff',
		marginHorizontal: 10,
		marginVertical: 5,
		padding: 8,
		borderColor: '#eee',
		borderWidth: 1,
		borderRadius: 5,
		flex: 1,
	},
	content: {
		paddingHorizontal: 4,
	},
	attr: {
		flexDirection: 'row',
		width: '100%',
		justifyContent: 'flex-end',
		alignItems: 'center',
	},
	price: {
		flex: 1,
		color: '#000',
		fontSize: 16,
	},
	voucher: {
		flex: 1,
		color: '#000',
		fontSize: 16,
		fontWeight: 'bold',
		textTransform: 'uppercase',
	},
	priceValue: {
		color: '#000',
		fontSize: 19,
		fontWeight: 'bold',
	},
	priceDecrease: {
		fontSize: 17,
		textAlign: 'right',
		color: '#555',
	},
	refundText: {
		marginTop: 10,
		fontWeight: 'bold',
		fontSize: 16,
	},
});
export default Detail;
