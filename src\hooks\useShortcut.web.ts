import { useEffect } from 'react';
import { register, unregister } from '@tauri-apps/api/globalShortcut';

import { isTauri } from '@/utils';

export const useShortcut = (
	key: string,
	callback: () => void,
	enabled: boolean = true,
) => {
	useEffect(() => {
		if (!enabled || !isTauri) {
			return;
		}
		const handleShortcut = async () => {
			await register(key, callback);
		};
		handleShortcut();

		return () => {
			unregister(key);
		};
	}, [key, enabled, callback]);
};
