import React from 'react';
import { Text, TouchableOpacity, View } from 'react-native';
import isEqual from 'react-fast-compare';
import { useDispatch } from 'react-redux';
import Icon from '@expo/vector-icons/Ionicons';
import { router } from 'expo-router';

import { setMember } from '@/store';
import { IMemberDetail } from '@/models';
import { primaryColor } from '@/constants';

import styles from '../styles';

type Props = {
	data: IMemberDetail;
};

const Row = React.memo(({ data }: Props) => {
	const dispatch = useDispatch();

	const onEdit = () => {
		router.navigate({
			pathname: '/cashier/members/edit',
			params: data as any,
		});
	};

	const onExchange = () => {
		dispatch(setMember(data));
		router.navigate({
			pathname: '/cashier/exchange',
			params: data as any,
		});
	};

	return (
		<View style={styles.row}>
			<View style={[styles.cell, styles.id]}>
				<Text>{data.member_id}</Text>
			</View>
			<View style={[styles.cell, styles.name]}>
				<Text>{data.name}</Text>
			</View>
			<View style={[styles.cell, styles.phone]}>
				<Text>{data.phone}</Text>
			</View>
			<View style={[styles.cell, styles.birthday]}>
				<Text>{data.birthday}</Text>
			</View>
			<View style={[styles.cell, styles.identification]}>
				<Text>{data.identification}</Text>
			</View>
			<View style={[styles.cell, styles.point]}>
				<Text>{data.point_left}</Text>
			</View>
			<View style={[styles.cell, styles.point]}>
				<Text>{data.point_level}</Text>
			</View>
			<View style={[styles.cell, styles.level]}>
				<Text>{data.level}</Text>
			</View>
			<View style={[styles.cell, styles.action]}>
				<TouchableOpacity style={styles.icon} onPress={onEdit}>
					<Icon name="create-outline" size={28} color="green" />
				</TouchableOpacity>
				<TouchableOpacity style={styles.icon} onPress={onExchange}>
					<Icon name="medal" size={28} color={primaryColor} />
				</TouchableOpacity>
			</View>
		</View>
	);
}, isEqual);

Row.displayName = 'MemberRow';

export default Row;
