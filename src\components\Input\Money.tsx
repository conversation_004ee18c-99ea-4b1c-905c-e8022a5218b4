import React from 'react';
import { StyleSheet } from 'react-native';
import { useSelector } from 'react-redux';

import { NumberInput } from './NumberInput';
import { getMoneyValue, updateMoneyQuantity, useAppDispatch } from '@/store';

type Props = {
	amount: number;
};
export const MoneyInput = React.memo(({ amount }: Props) => {
	const dispatch = useAppDispatch();
	const value = useSelector(getMoneyValue(amount));

	const onChange = (text: string) => {
		dispatch(updateMoneyQuantity({ name: amount, quantity: parseInt(text) }));
	};

	return (
		<NumberInput
			value={value.toString()}
			onChangeText={onChange}
			style={styles.input}
			keyboardType="numeric"
		/>
	);
});

MoneyInput.displayName = 'MoneyInput';

const styles = StyleSheet.create({
	input: {
		padding: 5,
		margin: 5,
		textAlign: 'center',
	},
});
