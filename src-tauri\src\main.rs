// Prevents additional console window on Windows in release, DO NOT REMOVE!!
#![cfg_attr(not(debug_assertions), windows_subsystem = "windows")]

use tauri::Manager;

fn main() {
  tauri::Builder::default()
    .setup(move |app| {
      #[cfg(debug_assertions)]
      app.get_window("main").unwrap().open_devtools();
      Ok(())
    })
    .on_window_event(|event| {
        if let tauri::WindowEvent::Destroyed = event.event() {
            if event.window().label() == "main" {
                std::process::exit(0x0);
            }
        }
    })
    .run(tauri::generate_context!())
    .expect("error while running tauri application");
}
