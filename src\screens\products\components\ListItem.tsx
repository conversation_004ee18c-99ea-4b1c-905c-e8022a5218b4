import React from 'react';
import { StyleSheet, View } from 'react-native';
import { useSelector } from 'react-redux';

import { getGroupData } from '@/store';
import { FlatList, ProductItem } from '@/components';
import { IProduct } from '@/models';

const ListItem = React.memo(({ id }: { id: number }) => {
	const products = useSelector(getGroupData(id));

	const renderItem = ({ item }: { item: IProduct }) => (
		<ProductItem data={item} width={150} />
	);

	if (products) {
		return (
			<View style={styles.list}>
				<FlatList
					keyExtractor={(item) => item.id}
					renderItem={renderItem}
					data={products.data}
					numColumns={4}
				/>
			</View>
		);
	}
	return null;
});

ListItem.displayName = 'ListItem';

const styles = StyleSheet.create({
	list: {
		flex: 1,
		borderRadius: 20,
		marginHorizontal: -2,
		overflow: 'hidden',
	},
});

export default ListItem;
