import React from 'react';
import { StyleSheet, View } from 'react-native';
import { useSelector } from 'react-redux';

import { RootState, getCartProducts, getTickets } from '@/store';
import { ListProducts } from '@/components';

import ListTickets from './ListTickets';

export const Products = React.memo(() => {
	const tickets = useSelector(getTickets);
	const products = useSelector(getCartProducts);
	const gifts = useSelector((state: RootState) => state.cart.gifts);

	return (
		<View style={styles.wrap}>
			{tickets.length > 0 && <ListTickets data={tickets} />}
			{(products.length > 0 || gifts.length > 0) && (
				<ListProducts data={[...products, ...gifts]} />
			)}
		</View>
	);
});

Products.displayName = 'CheckoutProducts';

const styles = StyleSheet.create({
	wrap: {
		flex: 1,
		marginHorizontal: 10,
		marginVertical: 5,
		borderColor: '#eee',
		borderWidth: 1,
		borderRadius: 5,
	},
});
