export interface IMemberForm {
	memberId: string;
	name: string;
	phone: string;
	email: string;
	identification: string;
	gender: string;
	birthday: string;
}

export interface IMemberDetail {
	id: string;
	member_id: string;
	name: string;
	phone: string;
	email: string;
	identification: string;
	gender: string;
	birthday: string;
	level: string;
	point_left: number;
	point_level: number;
}

export interface IMemberState {
	data: IMemberDetail[];
	links: object;
	meta: object;
}
