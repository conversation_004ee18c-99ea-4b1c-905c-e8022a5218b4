import { get, post } from './api';

const getSchedules = async () => {
	const response = await get('/cinema/timelines');
	return response;
};

const getShowtime = async (showtime_id: string) => {
	const response = await get('/cinema/maps?showtime_id=' + showtime_id);
	return {
		prices: response.data.prices,
		room: response.data.room,
		maps: response.data.maps,
		showtime: response.data.showtime,
	};
};

const reset = async () => {
	await post('/cinema/unselectall', []);
};

const getStatistics = async (date: string) => {
	const response = await get('/cinema/statistics?date=' + date);
	return response;
};

type HoldSeat = {
	price_id?: string;
	seat_id: number;
};

type HoldType = {
	seats: HoldSeat[];
	reason: string;
	print: boolean;
};
const holdSeats = async ({ seats, reason, print }: HoldType) => {
	const response = await post('/cinema/hold', {
		seats,
		reason,
		print,
	});
	return response;
};

export const bookingService = {
	getSchedules,
	getShowtime,
	reset,
	getStatistics,
	holdSeats,
};
