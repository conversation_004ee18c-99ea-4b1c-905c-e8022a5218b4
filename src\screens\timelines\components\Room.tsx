import React from 'react';
import { StyleSheet, Text, View } from 'react-native';

import type { RoomProps, IShowtime } from '../types';
import Showtime from './Showtime';

export const Room = React.memo(
	({ data, cellWidth, first = false }: RoomProps) => {
		return (
			<View style={[styles.row, first ? styles.first : null]}>
				<View style={styles.room}>
					<Text>Phòng {data.room}</Text>
				</View>
				<View style={styles.showtimes}>
					{data.data.map((showtime: IShowtime) => (
						<Showtime
							key={showtime.from}
							showtime={showtime}
							cellWidth={cellWidth}
						/>
					))}
				</View>
			</View>
		);
	},
);

Room.displayName = 'Room';

const styles = StyleSheet.create({
	row: {
		flex: 1,
		flexDirection: 'row',
		borderTopColor: '#eee',
		borderTopWidth: 2,
		maxHeight: 150,
	},
	first: {
		borderTopColor: '#ddd',
		borderTopWidth: 3,
	},
	room: {
		backgroundColor: '#ddd',
		width: 80,
		justifyContent: 'center',
		alignItems: 'center',
	},
	showtimes: {
		position: 'relative',
	},
});
