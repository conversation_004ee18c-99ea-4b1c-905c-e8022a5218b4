import React, { useState } from 'react';
import {
	ActivityIndicator,
	StyleSheet,
	Text,
	TouchableOpacity,
	View,
	ViewStyle,
	TextStyle,
} from 'react-native';
import { useSelector } from 'react-redux';
import isEqual from 'react-fast-compare';

import { useAppDispatch, getMember, setAmount, setMember } from '@/store';
import { cartService } from '@/services';
import { Button, Modal, TextInput } from '@/components';
import { showMessage } from '@/utils';
import { primaryColor } from '@/constants';

type Props = {
	orderId: number;
	text?: string;
	style?: ViewStyle;
	textStyle?: TextStyle;
};

export const VoucherButton = React.memo(
	({ orderId, text = 'Voucher', style, textStyle }: Props) => {
		const dispatch = useAppDispatch();

		const [modal, setModal] = useState(false);
		const [isLoading, setLoading] = useState(false);
		const [code, setCode] = useState('');
		const [seri, setSeri] = useState('');

		const member = useSelector(getMember);

		const openModal = () => {
			setModal(true);
		};

		const closeModal = () => {
			setModal(false);
		};

		const onSubmit = async () => {
			if (code.length === 0) {
				showMessage({
					message: 'Vui lòng nhập mã voucher!',
					type: 'error',
				});
				return;
			}
			setLoading(true);
			try {
				const response = await cartService.useVoucher({ code, seri, orderId });
				if (response.status === 1) {
					dispatch(
						setAmount({
							decrease: response.price.voucher,
							code: code,
						}),
					);
					if (response.price.point && response.price.point > 0) {
						dispatch(
							setMember({
								...member,
								point: response.price.point,
							}),
						);
					}
					closeModal();
				}
			} catch (e) {
				console.log(e);
			}
			setLoading(false);
		};

		return (
			<View style={[styles.btn, style]}>
				<TouchableOpacity onPress={openModal} style={styles.btnWrap}>
					<Text style={textStyle}>{text}</Text>
				</TouchableOpacity>

				<Modal visible={modal} onRequestClose={closeModal} style={styles.modal}>
					<View style={styles.modalBody}>
						{modal && (
							<>
								<View style={styles.inputWrap}>
									<TextInput
										value={code}
										onChangeText={setCode}
										placeholder="Nhập mã voucher"
										autoCapitalize="characters"
										autoFocus
										onSubmitEditing={onSubmit}
										clearButtonMode="always"
										style={styles.input}
									/>
								</View>
								<View style={styles.inputWrap}>
									<TextInput
										value={seri}
										onChangeText={setSeri}
										placeholder="Nhập seri"
										autoCapitalize="characters"
										clearButtonMode="always"
										onSubmitEditing={onSubmit}
										style={styles.input}
									/>
								</View>
							</>
						)}
						<Button onPress={onSubmit} text="Xác nhận" style={styles.submit} />
						{isLoading && (
							<View style={styles.loading}>
								<View style={styles.loader}>
									<ActivityIndicator size="large" color={primaryColor} />
								</View>
							</View>
						)}
					</View>
				</Modal>
			</View>
		);
	},
	isEqual,
);

VoucherButton.displayName = 'VoucherButton';

const styles = StyleSheet.create({
	bg: {
		flex: 1,
	},
	loading: {
		position: 'absolute',
		top: 0,
		left: 0,
		width: 350,
		height: 280,
		zIndex: 999,
		backgroundColor: 'rgba(0, 0, 0 ,.5)',
	},
	loader: {
		alignItems: 'center',
		flex: 1,
		justifyContent: 'center',
		top: '40%',
		position: 'absolute',
		width: '100%',
	},
	btn: {
		padding: 10,
		paddingHorizontal: 20,
		marginHorizontal: 10,
		borderRadius: 6,
		backgroundColor: primaryColor,
	},
	btnWrap: {
		alignItems: 'center',
		justifyContent: 'center',
		height: '100%',
		width: '100%',
	},
	modal: {},
	inputWrap: {
		marginBottom: 30,
	},
	modalBody: {
		backgroundColor: '#fff',
		padding: 40,
		width: 350,
		height: 280,
		alignItems: 'center',
		position: 'relative',
		borderRadius: 15,
	},
	input: {
		padding: 10,
		width: 250,
		borderColor: '#ddd',
		borderWidth: 1,
	},
	submit: {
		width: 140,
		alignItems: 'center',
	},
});
