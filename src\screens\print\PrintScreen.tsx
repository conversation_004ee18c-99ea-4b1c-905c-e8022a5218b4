import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { Picker } from '@react-native-picker/picker';
import { useDispatch, useSelector } from 'react-redux';

import { printService } from '@/services';
import { Button, TextInput } from '@/components';
import { showMessage, transformBill, transformTickets } from '@/utils';
import { ICartProduct } from '@/models';
import { getConfigs, getUser, setLoading } from '@/store';
import { primaryColor } from '@/constants';
import { printTask } from '@/modules/pos';

import { Results } from './components';
import { IPrintData, IPrintItem, IPrintShowtimes } from './types';

export const PrintScreen = () => {
	const dispatch = useDispatch();

	const configs = useSelector(getConfigs);
	const user = useSelector(getUser);

	const [type, setType] = useState<string>('ticket');

	const [orderId, setOrderId] = useState('');
	const [movies, setMovies] = useState([]);
	const [movie, setMovie] = useState('');
	const [showtimes, setShowtimes] = useState<IPrintShowtimes[]>([]);
	const [date, setDate] = useState<string>('');
	const [showtime, setShowtime] = useState('');
	const [data, setData] = useState<IPrintData>({
		tickets: [],
		products: [],
	});

	useEffect(() => {
		getMovies();
	}, []);

	useEffect(() => {
		if (movie) {
			getShowtimes();
		}
	}, [movie]); // eslint-disable-line react-hooks/exhaustive-deps

	const getMovies = async () => {
		const response = await printService.getMovies();
		setMovies(response);
	};

	const getShowtimes = async () => {
		const response = await printService.getShowtimes(movie);
		setShowtimes(response);
	};

	const onChangeText = useCallback((txt: string) => {
		setOrderId(txt);
	}, []);

	const dispatchSearch = () => {
		const input = orderId.toString().match(/(\d+)\s/i);
		if (input != null) {
			const code = input[0].toString().replace(' ', '');
			setOrderId(code);
			onSearch(code);
			return;
		}
		onSearch(orderId);
	};

	const showtimeInDate = useMemo(() => {
		if (date && typeof showtimes[parseInt(date)] !== 'undefined') {
			return showtimes[parseInt(date)].data;
		}
		return [];
	}, [showtimes, date]);

	const totalAmount = useMemo((): number => {
		if (data.products) {
			return data.products.reduce(
				(total, item: ICartProduct) =>
					total + (item.total_amount ? item.total_amount : 0),
				0,
			);
		}
		return 0;
	}, [data]);

	const onSearch = async (orderId: string) => {
		dispatch(setLoading(true));
		try {
			if (orderId.length > 0) {
				const response = await printService.searchOrder(orderId);
				setData(response);
			} else {
				const response = await printService.searchData(
					{ showtime_id: showtime },
					type,
				);
				setData({
					tickets: response,
					products: [],
				});
			}
		} catch (e) {
			console.log(e);
		}
		dispatch(setLoading(false));
	};

	const onPrintTickets = async () => {
		dispatch(setLoading(true));

		const tickets = [];

		for (const ticket of data.tickets) {
			if (ticket.status !== true && ticket.id) {
				try {
					const response = await printService.printTicket(ticket.id, type);
					tickets.push(response);
				} catch (e) {
					console.log(e);
				}
			}
		}

		if (tickets.length > 0) {
			await printTask(
				transformTickets({
					tickets,
					seller: user.name,
				}),
				configs.printer,
				'ticket',
			);
			showMessage({
				message: 'In vé thành công',
				type: 'success',
			});
		} else {
			showMessage({
				message: 'không có vé nào có thể in',
				type: 'warning',
			});
		}
		dispatch(setLoading(false));
	};

	const onPrintBill = async () => {
		if (data.bill) {
			dispatch(setLoading(true));
			try {
				const response = await printService.printBill(data.bill);
				await printTask(
					transformBill({
						counter: configs.counter,
						seller: user.name,
						products: data.products,
						bill: data.bill,
						time: response.time,
						order_id: orderId,
						member: response.member_id ? 'KH: ' + response.member_id : '',
						amount: {
							total: totalAmount,
							product: totalAmount,
							ticket: 0,
							input: totalAmount,
							refund: 0,
						},
					}),
					configs.printer,
					'bill',
				);
				showMessage({
					message: 'In hoá đơn thành công',
					type: 'success',
				});
			} catch (e) {
				console.log(e);
			}
			dispatch(setLoading(false));
		} else {
			showMessage({
				message: 'Không có hoá đơn có thể in',
				type: 'warning',
			});
		}
	};

	const onSetType = (itype: string) => () => {
		setType(itype);
	};

	return (
		<View style={styles.bg}>
			<View style={styles.box}>
				<View style={styles.search}>
					<Text style={styles.label}>Mã vé</Text>
					<TextInput
						value={orderId}
						onChangeText={onChangeText}
						style={styles.input}
						clearButtonMode="always"
						onSubmitEditing={dispatchSearch}
						autoFocus
					/>
					<Text style={styles.or}>Hoặc</Text>
					<Text style={styles.label}>Chọn phim</Text>
					<Picker
						selectedValue={movie}
						onValueChange={setMovie}
						style={styles.picker}>
						<Picker.Item label="Chọn" value="" />
						{movies.map((item: IPrintItem) => (
							<Picker.Item label={item.name} value={item.id} key={item.id} />
						))}
					</Picker>
					<Text style={styles.label}>Chọn ngày</Text>
					<Picker
						selectedValue={date}
						onValueChange={setDate}
						style={styles.picker}>
						<Picker.Item label="Chọn" value="" />
						{showtimes.map((item: IPrintShowtimes, index) => (
							<Picker.Item label={item.date} value={index} key={item.date} />
						))}
					</Picker>
					<Text style={styles.label}>Chọn suất chiếu</Text>
					<Picker
						selectedValue={showtime}
						onValueChange={setShowtime}
						style={styles.picker}>
						<Picker.Item label="Chọn" value="" />
						{showtimeInDate.map((item) => (
							<Picker.Item label={item.time} value={item.id} key={item.id} />
						))}
					</Picker>
					<View style={styles.row}>
						<TouchableOpacity
							onPress={onSetType('ticket')}
							style={[styles.tab, type === 'ticket' ? styles.tabActive : null]}>
							<Text
								style={[
									styles.tabLabel,
									type === 'ticket' ? styles.tabActive : null,
								]}>
								Ghế bán
							</Text>
						</TouchableOpacity>
						<TouchableOpacity
							onPress={onSetType('hold')}
							style={[styles.tab, type !== 'ticket' ? styles.tabActive : null]}>
							<Text
								style={[
									styles.tabLabel,
									type !== 'ticket' ? styles.tabActive : null,
								]}>
								Ghế giữ
							</Text>
						</TouchableOpacity>
					</View>
					<Button style={styles.btn} onPress={dispatchSearch}>
						<Text style={styles.btnLabel}>TÌM VÉ</Text>
					</Button>
					<View style={styles.action}>
						{data.tickets && data.tickets.length > 0 && (
							<Button style={styles.btn} onPress={onPrintTickets}>
								<Text style={styles.btnLabel}>In tất cả vé</Text>
							</Button>
						)}
						{data.bill && (
							<Button style={styles.btn} onPress={onPrintBill}>
								<Text style={styles.btnLabel}>In hoá đơn</Text>
							</Button>
						)}
					</View>
				</View>
			</View>
			<Results data={data} type={type} />
		</View>
	);
};

const styles = StyleSheet.create({
	bg: {
		flex: 1,
		flexDirection: 'row',
		paddingHorizontal: 10,
		paddingVertical: 15,
	},
	box: {
		backgroundColor: '#fff',
		width: 350,
		marginHorizontal: 10,
		padding: 15,
		borderRadius: 5,
	},
	search: {
		flex: 1,
	},
	input: {
		padding: 10,
		borderColor: '#ddd',
		borderWidth: 1,
	},
	or: {
		textAlign: 'center',
		paddingVertical: 10,
	},
	label: {},
	picker: {
		marginBottom: 10,
	},
	btn: {
		borderRadius: 3,
		marginHorizontal: 0,
		marginVertical: 10,
	},
	btnLabel: {
		fontSize: 16,
		color: '#fff',
		textAlign: 'center',
	},
	action: {
		flex: 1,
		justifyContent: 'flex-end',
	},
	row: {
		flexDirection: 'row',
		marginHorizontal: -5,
	},
	tab: {
		flex: 1,
		margin: 5,
		borderColor: '#ddd',
		borderWidth: 1,
		borderRadius: 3,
		padding: 8,
		alignItems: 'center',
		backgroundColor: '#fff',
	},
	tabLabel: {
		color: '#000',
	},
	tabActive: {
		borderColor: primaryColor,
		color: primaryColor,
	},
});
