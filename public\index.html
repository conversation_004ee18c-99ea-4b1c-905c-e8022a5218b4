<!doctype html>
<html lang="vi">
	<head>
		<meta charset="utf-8" />
		<meta httpEquiv="X-UA-Compatible" content="IE=edge" />
		<meta
			name="viewport"
			content="width=device-width, initial-scale=1, shrink-to-fit=no" />
		<title>TouchCinema POS</title>
		<link rel="stylesheet" href="/styles.css" />
		<script>
			function disableBackButton() {
				window.addEventListener('popstate', (event) => {
					event.preventDefault();
					event.stopImmediatePropagation();
				});
			}

			disableBackButton();
		</script>
	</head>

	<body oncontextmenu="return false;">
		<!-- Use static rendering with Expo Router to support running without JavaScript. -->
		<noscript> You need to enable JavaScript to run this app. </noscript>
		<!-- The root element for your Expo app. -->
		<div id="root"></div>
	</body>
</html>
