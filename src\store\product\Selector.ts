import { createSelector } from '@reduxjs/toolkit';

import { RootState } from '@/store';
import { IProductGroup } from '@/models';

const productsSelector = (state: RootState) => state.products;

export const getProducts = (state: RootState) => state.products.products;

export const getGroup = (state: RootState) => state.products.group;

export const getGroupData = (id: number) => {
	return createSelector(productsSelector, (products) =>
		products.group.find((item: IProductGroup) => item.id === id),
	);
};

export const getGifts = (state: RootState) => state.products.gifts;

export const getCombo = (state: RootState) => state.products.combo;
