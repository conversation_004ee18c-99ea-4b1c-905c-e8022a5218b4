export enum ActionType {
	SHOW = 'SHOW_ALERT',
	HIDE = 'HIDE_ALERT',
}

export type AlertState = {
	isReady: boolean;
	title: string;
	message: string;
	buttons: any[];
	visible: boolean;
	icon: boolean;
	dispatch(params?: any): void;
};

export type AlertButton = {
	onPress(args?: any): any;
	text: string;
};

export type AlertAction = {
	type: ActionType;
	payload?: any;
};

export interface IAlertContext {
	state: AlertState;
	dispatch: React.Dispatch<AlertAction>;
}

export type ShowAlertProps = {
	title: string;
	message: string;
	buttons?: any[];
};
