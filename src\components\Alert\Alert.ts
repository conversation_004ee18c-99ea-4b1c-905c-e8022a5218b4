import { Alert as NativeAlert, Platform } from 'react-native';

import { ActionType } from './types';
import { initialState } from './AlertProvider';

const alertPolyfill = {
	alert(title: string, message: string, buttons: any, extra?: any) {
		initialState.dispatch({
			type: ActionType.SHOW,
			payload: {
				title,
				message,
				buttons,
			},
		});
	},
};

export const Alert = Platform.OS === 'web' ? alertPolyfill : NativeAlert;
