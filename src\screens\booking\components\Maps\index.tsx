import React, { useState } from 'react';
import {
	View,
	Text,
	StyleSheet,
	ScrollView,
	ImageBackground,
} from 'react-native';
import isEqual from 'react-fast-compare';
import { useSelector } from 'react-redux';

import { getRoom } from '@/store';

import MapSeats from './Seats';

export const Maps = React.memo(() => {
	const room = useSelector(getRoom);
	const [viewWidth, setViewWidth] = useState(1);

	return (
		<View style={styles.wrap}>
			<View style={styles.detail}>
				<Text style={styles.room}>PHÒNG CHIẾU {room}</Text>
				<View style={styles.screen}>
					<ImageBackground
						source={require('@/assets/images/backgrounds/screen.png')}
						style={styles.bgScreen}>
						<Text style={styles.screenText}>MÀN HÌNH</Text>
					</ImageBackground>
				</View>
			</View>
			<View
				style={styles.maps}
				onLayout={(event) => {
					const { width } = event.nativeEvent.layout;
					setViewWidth(width);
				}}>
				<ScrollView
					showsVerticalScrollIndicator={false}
					showsHorizontalScrollIndicator={false}>
					<MapSeats viewWidth={viewWidth} />
				</ScrollView>
			</View>
		</View>
	);
}, isEqual);

Maps.displayName = 'Maps';

const styles = StyleSheet.create({
	wrap: {
		flex: 1,
		position: 'relative',
	},
	maps: {
		flex: 1,
		marginRight: 10,
		marginTop: 40,
	},
	detail: {
		position: 'absolute',
		top: 0,
		left: 0,
		width: '100%',
	},
	room: {
		fontSize: 25,
		color: '#000',
		textAlign: 'center',
		paddingVertical: 5,
		marginLeft: 20,
	},
	screen: {
		width: '70%',
		left: '17%',
		alignContent: 'center',
		height: 75,
	},
	bgScreen: {
		width: '100%',
		height: '100%',
		resizeMode: 'stretch',
	},
	screenText: {
		marginTop: 20,
		color: '#000',
		fontSize: 20,
		textAlign: 'center',
	},
});
