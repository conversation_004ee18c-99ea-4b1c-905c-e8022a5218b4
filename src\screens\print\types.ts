import { ICartProduct, ICartTicket } from '@/models';

export interface IPrintData {
	tickets: ICartTicket[];
	products: ICartProduct[];
	bill?: string;
}

export interface IPrintShowtimes {
	id: string;
	data: IPrintShowtime[];
	date: string;
}

export interface IPrintShowtime {
	id: string;
	time: string;
}

export interface IPrintItem {
	id: string;
	name: string;
}

export interface IPrintTicket {
	id: number;
	status: boolean;
	seat_name: string;
	ticket_id: string;
	room_name: string;
	time: string;
	date: string;
	movie_name_vn: string;
	create_date: string;
}
