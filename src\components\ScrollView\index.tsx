import React from 'react';
import {
	ScrollView as NativeScrollView,
	ScrollViewProps as NativeScrollViewProps,
} from 'react-native';

interface WebScrollViewProps {
	showScroller?: boolean;
}

type ScropViewProps = NativeScrollViewProps & WebScrollViewProps;

export const ScrollView = React.memo((props: ScropViewProps) => {
	return <NativeScrollView {...props} />;
});

ScrollView.displayName = 'ScrollView';
