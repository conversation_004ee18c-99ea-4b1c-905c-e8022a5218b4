// Learn more https://docs.expo.io/guides/customizing-metro
const { getDefaultConfig } = require('expo/metro-config');

/** @type {import('expo/metro-config').MetroConfig} */
const config = getDefaultConfig(__dirname);

config.transformer.minifierConfig = {
	compress: {
		// The option below removes all console logs statements in production.
		drop_console: true,
	},
};

// Ensure blockList is an array before concatenating
config.resolver.blockList = Array.isArray(config.resolver.blockList)
	? config.resolver.blockList.concat([/\/src-tauri\//])
	: [/\/src-tauri\//];

module.exports = config;
