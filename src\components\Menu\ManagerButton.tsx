import React from 'react';
import Icon from '@expo/vector-icons/Ionicons';
import { WebviewWindow } from '@tauri-apps/api/window';

import MenuItem from './MenuItem';

const ManagerButton = React.memo(() => {
	const renderIcon = ({ color, size }: { color: string; size: number }) => (
		<Icon size={size} name="person-outline" color={color} />
	);

	const onPress = async () => {
		new WebviewWindow('manager', {
			url: 'http://new.touchcinema.com/quanly',
			title: 'Manager',
		});
	};

	return (
		<MenuItem
			icon={renderIcon}
			onPress={onPress}
			label={'Manager'}
			isFocused={false}
		/>
	);
});

ManagerButton.displayName = 'ManagerButton';

export default ManagerButton;
