import React from 'react';
import { View, ViewStyle } from 'react-native';

type SeparatorProps = {
	width?: number;
	color?: string;
};

export const Separator = React.memo(
	({ width = 1, color = '#eee' }: SeparatorProps) => {
		const style: ViewStyle = {
			height: width,
			width: '100%',
			backgroundColor: color,
		};

		return <View style={style} />;
	},
);

Separator.displayName = 'Separator';
