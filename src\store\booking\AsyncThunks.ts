import { createAsyncThunk } from '@reduxjs/toolkit';

import { bookingService } from '@/services';
import { setLoading } from '@/store';

export const getShowtime = createAsyncThunk(
	'booking/getShowtime',
	async (args: string, thunkApi) => {
		thunkApi.dispatch(setLoading(true));
		try {
			const response = await bookingService.getShowtime(args);
			thunkApi.dispatch(setLoading(false));
			return response;
		} catch (e) {
			console.log(e);
		}
		thunkApi.dispatch(setLoading(false));
		return thunkApi.rejectWithValue('Error');
	},
);
