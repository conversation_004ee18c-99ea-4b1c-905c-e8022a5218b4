import { useEffect } from 'react';
import { StyleSheet, View } from 'react-native';
import { Redirect, Stack, usePathname } from 'expo-router';
import { useSelector } from 'react-redux';

import { getUser } from '@/store';
import { AlertProvider, Menu, Toast } from '@/components';
import { sendToCustomer } from '@/services';

export default function POSLayout() {
	const user = useSelector(getUser);

	const pathname = usePathname();

	useEffect(() => {
		sendToCustomer({
			route: pathname.replace('/cashier', ''),
		});
	}, [pathname]);

	if (!user.name) {
		return <Redirect href="/" />;
	}

	return (
		<View style={styles.bg}>
			<Menu />
			<Stack screenOptions={{ headerShown: false }}>
				<Stack.Screen
					name="money"
					options={{
						presentation: 'transparentModal',
					}}
				/>
			</Stack>
			<Toast visibilityTime={2500} topOffset={20} />
			<AlertProvider />
		</View>
	);
}

const styles = StyleSheet.create({
	bg: {
		flex: 1,
		flexDirection: 'row',
	},
});
