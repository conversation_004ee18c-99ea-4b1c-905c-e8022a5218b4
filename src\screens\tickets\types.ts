export interface IShowtime {
	id: string;
	status: boolean;
	time: string;
	date: string;
	end: string;
}

export interface IShowtimes {
	data: {
		showtime: IShowtime[];
		poster: string;
		name: string;
		age: string;
	};
}

export interface IMovie {
	name: string;
	duration?: string;
	poster: string;
	showtime: IShowtime[];
	age: string;
}

export interface Route {
	key: string;
	title: string;
	day: string;
	month: string;
}

export interface ILabel {
	route: Route;
	focused: boolean;
}

export interface ITimeline {
	date: string;
	movies: IMovie[];
}
