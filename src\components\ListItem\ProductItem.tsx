import React, { memo } from 'react';
import { Image, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { useDispatch } from 'react-redux';

import { currencyFormat } from '@/utils';
import { addProduct, addGift } from '@/store';

import { IProductItem } from './types';

export const ProductItem = memo(
	({ data, type = 'product', width }: IProductItem) => {
		const dispatch = useDispatch();

		const onSelect = () => {
			if (type === 'product') {
				dispatch(
					addProduct({
						type: 'increment',
						item: data,
					}),
				);
			} else {
				dispatch(
					addGift({
						type: 'increment',
						item: data,
					}),
				);
			}
		};

		return (
			<View style={[styles.item, width ? { width } : null]}>
				<TouchableOpacity onPress={onSelect} style={styles.itemContainer}>
					<Image
						source={{
							uri: data.image,
						}}
						style={styles.image}
					/>
					<Text style={styles.text}>{data.name}</Text>
					<View style={styles.boxPrice}>
						<Text style={[styles.text, styles.price]}>
							{data.point
								? data.point + ' điểm'
								: currencyFormat(data.price) + ' đ'}
						</Text>
					</View>
				</TouchableOpacity>
			</View>
		);
	},
);

ProductItem.displayName = 'ProductItem';

const styles = StyleSheet.create({
	item: {
		flex: 1,
		flexDirection: 'column',
		margin: 8,
		backgroundColor: '#fff',
		borderRadius: 5,
		paddingVertical: 5,
		paddingHorizontal: 10,
		elevation: 3,
	},
	itemContainer: {
		alignItems: 'center',
		width: '100%',
		flex: 1,
	},
	text: {
		textAlign: 'center',
		width: '100%',
	},
	boxPrice: {
		flex: 1,
		width: '100%',
		justifyContent: 'flex-end',
	},
	price: {
		color: '#444',
		borderTopWidth: 1,
		borderTopColor: '#ddd',
		marginTop: 10,
		paddingTop: 8,
	},
	image: {
		width: 80,
		height: 80,
		borderRadius: 80,
	},
});
