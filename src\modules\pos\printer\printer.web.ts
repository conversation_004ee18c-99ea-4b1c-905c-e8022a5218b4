import { Command } from '@tauri-apps/api/shell';
import { writeTextFile } from '@tauri-apps/api/fs';
import { appLocalDataDir, join } from '@tauri-apps/api/path';
import { htmlEncode } from 'js-htmlencode';

import { isTauri } from '@/utils';

import parse from './templates';
import {
	IBill,
	IMoney,
	IProduct,
	IReport,
	IPoint,
	ITicket,
	PrinterDriver,
	PrinterType,
} from './types';

export const printTask = async (
	data: IBill | ITicket[] | IReport | IProduct | IMoney | IPoint,
	printer: string,
	template: PrinterType,
	driverName: PrinterDriver = 'native',
) => {
	const raw: string = await parse(template, data, printer);
	await toPrinter(raw, driverName, template);
};

export const toPrinter = async (
	data: string,
	driver: PrinterDriver = 'native',
	name: string = 'tickets',
): Promise<void> => {
	if (isTauri) {
		let command;
		if (data.length > 7500) {
			console.log('print via ' + driver + ' with xml');
			const file = await writeFile(name + '.xml', data);
			command = new Command('tw-printer', ['xml', file, 'clean']);
		} else {
			console.log('print via ' + driver + ' with raw');
			command = new Command('tw-printer', ['raw', htmlEncode(data)]);
		}
		try {
			const result = await command.execute();
			console.log(result);
		} catch (err) {
			console.error(err);
		}
	} else {
		console.log('toPrinter is not supported in browser');
	}
};

export const getPrinters = async (): Promise<string[]> => {
	if (isTauri) {
		const output = await new Command('get-printers', [
			'Get-Printer | ConvertTo-Json',
		]).execute();
		if (output.code === 0) {
			const data = JSON.parse(output.stdout);

			try {
				let printers = data.map((item: any) => item.Name);
				return printers;
			} catch (error) {
				if (data.Name !== undefined) {
					return [data.Name];
				}
				console.error(error);
			}
			return [];
		}
	} else {
		console.log('getPrinter is not supported in browser');
	}

	return [];
};

export const writeFile = async (file: string, content: string) => {
	const filePath = await join(await appLocalDataDir(), file);
	await writeTextFile(filePath, content);
	return filePath;
};
