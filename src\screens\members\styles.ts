import { StyleSheet } from 'react-native';

const styles = StyleSheet.create({
	bg: {
		flex: 1,
		overflow: 'hidden',
	},
	list: {
		flex: 1,
		borderBottomColor: '#ddd',
		borderBottomWidth: 1,
	},
	row: {
		flexDirection: 'row',
	},
	header: {
		backgroundColor: '#f5f5f5',
		borderTopColor: '#ddd',
		borderTopWidth: 1,
	},
	cell: {
		padding: 8,
		borderColor: '#ddd',
		borderRightWidth: 1,
		borderBottomWidth: 1,
		width: 100,
		justifyContent: 'center',
	},
	id: {
		width: 95,
		borderLeftWidth: 1,
		borderColor: '#ddd',
	},
	name: {
		flex: 1,
	},
	phone: {
		width: 100,
	},
	point: {
		width: 70,
	},
	identification: {
		width: 110,
	},
	level: {
		width: 85,
	},
	birthday: {},
	action: {
		width: 100,
		flexDirection: 'row',
	},
	actionHeader: {
		marginRight: 7,
	},
	icon: {
		flex: 1,
		alignItems: 'center',
	},
});

export default styles;
