import { createAsyncThunk } from '@reduxjs/toolkit';
import { get, post } from '@/services';
import { IReport, IReportShift } from '@/models';
import { setLoading } from '@/store';

import { setShifts, setReportData } from './Slice';

// Fetch shifts async thunk
export const fetchShifts = createAsyncThunk(
	'report/fetchShifts',
	async (_, { dispatch, rejectWithValue }) => {
		dispatch(setLoading(true));

		try {
			const response = await get('shifts');
			const shifts: IReportShift[] = response.data;

			dispatch(setShifts(shifts));
			dispatch(setLoading(false));
			return shifts;
		} catch (error) {
			const errorMessage =
				error instanceof Error ? error.message : 'Failed to fetch shifts';
			dispatch(setLoading(false));
			return rejectWithValue(errorMessage);
		}
	},
);

// Generate report async thunk
export const generateReport = createAsyncThunk(
	'report/generateReport',
	async (
		params: { from: string; to: string; shift: number },
		{ dispatch, rejectWithValue },
	) => {
		dispatch(setLoading(true));

		try {
			const response = await post('report', params);

			const reportData: IReport = {
				detail: {
					start: response.data.from,
					end: response.data.to,
					user: response.data.fullname,
					amount: response.data.amount,
					shift: response.data.shift,
				},
				tickets: response.data.tickets,
				products: response.data['foods-drinks'],
				overview: response.data.products,
				categories: response.data.categories,
				total_categories: response.data.total_categories,
			};

			dispatch(setReportData(reportData));
			dispatch(setLoading(false));
			return reportData;
		} catch (error) {
			const errorMessage =
				error instanceof Error ? error.message : 'Failed to generate report';
			dispatch(setLoading(false));
			return rejectWithValue(errorMessage);
		}
	},
);
