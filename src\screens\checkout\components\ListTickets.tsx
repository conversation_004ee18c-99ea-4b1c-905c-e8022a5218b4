import React from 'react';
import { StyleSheet, Text, View, SectionList } from 'react-native';
import isEqual from 'react-fast-compare';

import { Separator } from '@/components';
import { ICartTicket } from '@/models';

import TicketItem from './TicketItem';

type Props = {
	data: ICartTicket[];
};

const ListTickets = React.memo(({ data }: Props) => {
	let tickets: any[] = [];
	let tmp: Record<string, any> = {};
	for (const item of data) {
		if (item.showtime_id) {
			if (typeof tmp[item.showtime_id] === 'undefined') {
				tmp[item.showtime_id] = {
					title: `${item.time} - ${item.movie_name_vn} ${item.movie_age || ''}`,
					data: [item],
				};
			} else {
				tmp[item.showtime_id].data.push(item);
			}
		}
	}
	tickets = Object.values(tmp);

	const renderItem = ({ item }: { item: ICartTicket }) => (
		<TicketItem item={item} />
	);
	return (
		<View style={styles.wrap}>
			<View style={styles.header}>
				<Text style={styles.title}>VÉ ({data.length})</Text>
			</View>
			<View style={styles.list}>
				<SectionList
					sections={tickets}
					keyExtractor={(_, index) => 'ticket-' + index}
					renderItem={renderItem}
					ItemSeparatorComponent={Separator}
					renderSectionHeader={({
						section: { title },
					}: {
						section: { title: string };
					}) => (
						<Text style={styles.movie} numberOfLines={1}>
							{title}
						</Text>
					)}
				/>
			</View>
		</View>
	);
}, isEqual);

ListTickets.displayName = 'ListTickets';

const styles = StyleSheet.create({
	wrap: {
		flex: 1,
	},
	header: {
		backgroundColor: '#ddd',
		paddingHorizontal: 15,
		paddingVertical: 4,
	},
	title: {
		fontSize: 17,
	},
	list: {
		flex: 1,
		paddingHorizontal: 15,
		backgroundColor: '#fff',
	},
	movie: {
		fontSize: 18,
		marginTop: 5,
		paddingBottom: 5,
		color: 'red',
		borderBottomColor: '#ddd',
		borderBottomWidth: 1,
	},
});

export default ListTickets;
