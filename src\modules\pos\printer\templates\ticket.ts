import { isTauri } from '@/utils';
import { ITicket } from '../types';

const parseTickets = async (tickets: ITicket[], printer: string) => {
	let logo: string = './logo-ticket.png';
	if (isTauri) {
		const { resolveResource } = await import('@tauri-apps/api/path');
		logo = await resolveResource('logo-ticket.png');
		logo = logo.replace(/^\\\\\?\\/gs, '');
	}
	let xml: string = '<?xml version="1.0" encoding="utf-8" ?><Receipts>';

	for (let ticket of tickets) {
		xml += parseTicket(ticket, printer, logo);
	}

	xml += '</Receipts>';
	return xml;
};

const parseTicket = (ticket: ITicket, printer: string, logo: string) => {
	return `
  <Receipt name="${
		ticket.ticket_id || ticket.order_id
	}" printer="${printer}" left="10">
      <Image width="180" height="97" align="center">${logo}</Image>
      <Space height="8"/>
      <Text align="center" style="bold" size="12">THẺ VÀO PHÒNG CHIẾU PHIM</Text>
      <Space height="6"/>
      <Text style="bold">CÔNG TY TNHH MTV QUỲNH NGUYÊN GIA LAI</Text>
      <Text>212 Nguyễn Tất Thành, P.Phù Đổng, TP.Pleiku, Gia Lai</Text>
      <Space height="6"/>
      <Line type="dashed"/>
      <Space height="6"/>
      <Table template="1,1">
          <Tr>
              <Td>
                  <Text>POS: ${ticket.seller}</Text>
                  <Text size="8">${ticket.payment}</Text>
              </Td>
              <Td>
                  <Text align="right">Mã GD: ${ticket.order_id}</Text>
              </Td>
          </Tr>
          <Tr>
              <Td>
                  <Text>${ticket.create_date}   ${ticket.times}</Text>
              </Td>
              <Td>
                  <Text align="right">${ticket.count || ''}</Text>
              </Td>
          </Tr>
      </Table>
      <Space height="6"/>
      <Line type="dashed"/>
      <Space height="6"/>
      <Text style="bold" size="14">${ticket.movie}</Text>
      <Text size="12">Ngày: ${ticket.date}       ${ticket.time}</Text>
      <Text size="12" style="bold">Phòng: ${ticket.room}       Số ghế: ${ticket.seat}</Text>
      <Text size="11">Giá vé: ${ticket.type}  ${ticket.price} đ</Text>
      <Space height="6"/>
      <Line type="dashed"/>
      <Space height="6"/>
      <Text size="9" align="right">STT: ${ticket.stt}</Text>
  </Receipt>`;
};
export default parseTickets;
