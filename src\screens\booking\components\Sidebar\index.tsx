import React, { useCallback, useMemo, useState } from 'react';
import {
	SectionList,
	StyleSheet,
	Text,
	TouchableOpacity,
	View,
} from 'react-native';
import { useSelector } from 'react-redux';
import Ionicons from '@expo/vector-icons/Ionicons';

import {
	useAppDispatch,
	getSelected,
	changePrices,
	getShowtimes,
} from '@/store';
import { BoxAction, Separator, Modal } from '@/components';
import { primaryColor } from '@/constants';
import {
	IBookingSeat,
	IBookingSelectedGroup,
	IBookingShowtime,
} from '@/models';

import Seat from './Seat';
import ModalBulks from './ModalBulks';

export const Sidebar = React.memo(() => {
	const dispatch = useAppDispatch();
	const seats = useSelector(getSelected);
	const showtimes = useSelector(getShowtimes);

	const [modal, setModal] = useState<boolean>(false);

	const onBulks = () => {
		setModal(true);
	};

	const onChangePrice = useCallback((price: string) => {
		dispatch(changePrices(price));
		setModal(false);
	}, []); // eslint-disable-line react-hooks/exhaustive-deps

	const closeModal = useCallback(() => {
		setModal(false);
	}, []);

	const data: IBookingSelectedGroup[] = useMemo(() => {
		return Object.values(
			seats.reduce(function (result: any, item: IBookingSeat) {
				if (item.showtime_id) {
					if (typeof result[item.showtime_id] === 'undefined') {
						const showtime: IBookingShowtime | undefined = showtimes.find(
							(s: IBookingShowtime) => s.id === item.showtime_id,
						);
						const group: IBookingSelectedGroup = {
							id: item.showtime_id,
							title:
								typeof showtime !== 'undefined'
									? `${showtime.time} - ${showtime.movie}`
									: 'Không xác định',
							data: [],
						};
						result[item.showtime_id] = group;
					}
					result[item.showtime_id].data.push(item);
				}
				return result;
			}, {}),
		);
	}, [seats, showtimes]);

	const renderItem = ({ item }: { item: IBookingSeat }) => <Seat seat={item} />;
	const renderHeader = ({
		section: { title },
	}: {
		section: { title: string };
	}) => (
		<View style={styles.movie}>
			<Text style={styles.movieTitle}>{title}</Text>
		</View>
	);

	return (
		<View style={styles.bg}>
			<View style={styles.header}>
				<Text style={styles.headerText}>Ghế chọn ({seats.length})</Text>
				<View>
					<TouchableOpacity onPress={onBulks} style={styles.bulks}>
						<Ionicons name="options" size={30} color={primaryColor} />
					</TouchableOpacity>
				</View>
			</View>
			<SectionList
				sections={data}
				keyExtractor={(item: IBookingSeat, index) =>
					item.id.toString() + index.toString()
				}
				renderItem={renderItem}
				renderSectionHeader={renderHeader}
				ItemSeparatorComponent={Separator}
			/>
			<BoxAction ticket={true} />
			<Modal visible={modal} onRequestClose={closeModal} style={styles.modal}>
				<ModalBulks onChange={onChangePrice} />
			</Modal>
		</View>
	);
});

Sidebar.displayName = 'BookingSidebar';

const styles = StyleSheet.create({
	bg: {
		backgroundColor: '#fff',
		height: '100%',
	},
	header: {
		paddingHorizontal: 10,
		flexDirection: 'row',
		height: 45,
		justifyContent: 'space-between',
	},
	headerText: {
		fontSize: 20,
		lineHeight: 40,
		color: primaryColor,
	},
	movie: {
		backgroundColor: '#ddd',
		paddingHorizontal: 10,
		paddingVertical: 4,
		fontSize: 16,
	},
	movieTitle: {},
	bulks: {
		paddingTop: 5,
	},
	modal: {},
});
