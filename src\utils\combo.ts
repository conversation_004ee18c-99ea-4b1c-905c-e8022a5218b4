import { <PERSON>uffer } from 'buffer';
import { IProduct, IProductGroup } from '@/models';
import { Configs } from '@/constants';

const drinks: string[] = ['Pepsi', '7up', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>'];

export const parseCombo = (data: IProductGroup[] | undefined) => {
	const combo: IProduct[] = [];
	if (data) {
		data.forEach((group) => {
			if (group.id === Configs.combo_id) {
				group.data.forEach((item) => {
					if (item.description) {
						item.description.split(',').forEach((product) => {
							const name: string = product.replace(/^\s?[\d]+/gm, '').trim();
							const id: string = Buffer.from(name).toString('base64');
							const type: string = drinks.some(
								(i) => name.toUpperCase().search(i.toUpperCase()) >= 0,
							)
								? 'drink'
								: 'popcorn';
							if (!combo.some((i) => i.name === name)) {
								combo.push({
									id,
									type,
									quantity: 0,
									price: 0,
									image: findImage(name, data),
									name,
								});
							}
						});
					}
				});
			}
		});
	}

	return combo;
};

const findImage = (name: string, data: IProductGroup[] | undefined): string => {
	if (data) {
		name = name.replace('Bắp rang bơ', 'Bắp Ngọt');
		for (const group of data) {
			if (group.id !== Configs.combo_id) {
				for (const item of group.data) {
					if (item.name.toUpperCase() === name.toUpperCase()) {
						if (item.image) {
							return item.image;
						}
					}
				}
			}
		}
	}
	return Configs.default_image;
};
