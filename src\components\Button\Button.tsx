import React from 'react';
import {
	Text,
	TouchableOpacity,
	StyleSheet,
	ViewStyle,
	TextStyle,
} from 'react-native';
import isEqual from 'react-fast-compare';

import { primaryColor } from '@/constants';

type ButtonProps = {
	text?: string;
	onPress(): void | null | Promise<void>;
	children?: string | JSX.Element | JSX.Element[];
	style?: ViewStyle;
	textStyle?: TextStyle;
	disabled?: boolean;
	background?: string;
};

export const Button = React.memo(
	({
		text = '',
		onPress,
		children,
		background = primaryColor,
		style = {},
		textStyle = {
			color: '#fff',
			fontSize: 16,
			textAlign: 'center',
		},
		disabled = false,
	}: ButtonProps) => {
		const bg = {
			backgroundColor: background,
		};
		if (disabled) {
			return (
				<TouchableOpacity style={[styles.btn, style, bg, styles.disabled]}>
					{children}
					{text.length > 0 && <Text style={textStyle}>{text}</Text>}
				</TouchableOpacity>
			);
		} else {
			return (
				<TouchableOpacity onPress={onPress} style={[styles.btn, style, bg]}>
					{children}
					{text.length > 0 && <Text style={textStyle}>{text}</Text>}
				</TouchableOpacity>
			);
		}
	},
	isEqual,
);

Button.displayName = 'Button';

const styles = StyleSheet.create({
	btn: {
		padding: 10,
		paddingHorizontal: 20,
		marginHorizontal: 10,
		borderRadius: 6,
	},
	disabled: {
		backgroundColor: '#b15884',
		cursor: 'not-allowed',
	},
});
