import { ICartProduct, ICartTicket } from './cart';

export interface IReportAmount {
	amount_sell: number;
	amount_cash: number;
	amount_card: number;
	card: number;
	momo?: number;
	vnpay?: number;
	shopeepay?: number;
	zalopay?: number;
	grab?: number;
	beamin?: number;
	amount_ticket: number;
	amount_combo: number;
	amount_total_cancel: number;
	voucher_count: number;
	amount_voucher: number;
	amount_total: number;
}

export interface IReportDetail {
	start: string;
	end: string;
	user: string;
	amount: IReportAmount;
	shift: string;
}
export interface IReport {
	detail: IReportDetail;
	tickets: {
		success?: ICartTicket[];
		canceled?: ICartTicket[];
	};
	products: {
		success?: ICartProduct[];
	};
	overview: IReportItem[];
	categories: IReportItem[];
	total_categories: number;
}

export interface IReportItem {
	name: string;
	total: number;
}

export interface IReportShift {
	name: string;
	id: number;
}

export interface TotalAmount {
	tickets: number;
	products: number;
}
