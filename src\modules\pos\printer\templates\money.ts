import { IMoney } from '../types';

const parseMoney = async (data: <PERSON><PERSON><PERSON>, printer: string) => {
	return `<Receipts>
  <Receipt name="Money" printer="${printer}" left="10">
      <Text size="12" style="bold" align="center">TOUCH CINEMA</Text>
      <Text style="bold" align="center">212 <PERSON><PERSON>, P.<PERSON>, TP.<PERSON>, Gia <PERSON></Text>
      <Text size="8" align="center">www.touchcinema.com</Text>
      <Space height="8"/>
      <Text size="15" style="bold" align="center">BẢNG KÊ TIỀN</Text>
      <Space height="10"/>
      <Table template="3,7">
          <Tr>
              <Td>
                  <Text style="bold">Giờ:</Text>
              </Td>
              <Td>
                  <Text>${data.time}</Text>
              </Td>
          </Tr>
          <Tr>
              <Td>
                  <Text style="bold">Quầy:</Text>
              </Td>
              <Td>
                  <Text>${data.counter}</Text>
              </Td>
          </Tr>
          <Tr>
              <Td>
                  <Text style="bold">Nhân viên:</Text>
              </Td>
              <Td>
                  <Text>${data.seller}</Text>
              </Td>
          </Tr>
      </Table>
      <Space height="10"/>
      <Line type="dashed"/>
      <Space height="6"/>
      <Table template="9,8,10">
          <Tr>
              <Td>
                  <Text align="center" size="8">Mệnh giá</Text>
              </Td>
              <Td>
                  <Text align="center" size="8">Số lượng</Text>
              </Td>
              <Td>
                  <Text align="center" size="8">Số tiền</Text>
              </Td>
          </Tr>
      </Table>
      <Space height="5"/>
      <Line type="dashed"/>
      <Space height="5"/>
      <Table template="9,8,10">
          ${data.data
						.map(
							(row: any) => `
          <Tr>
              <Td>
                  <Text align="center">${row[0]}</Text>
              </Td>
              <Td>
                  <Text align="center">${row[1]}</Text>
              </Td>
              <Td>
                  <Text align="center">${row[2]}</Text>
              </Td>
          </Tr>`,
						)
						.join('')}
      </Table>
      <Space height="5"/>
      <Line type="dashed"/>
      <Space height="5"/>
      <Table template="9,8,10">
          <Tr>
              <Td>
                  <Text align="center" style="bold">Tổng cộng</Text>
              </Td>
              <Td>
                  <Text align="center" style="bold">${
										data.total_quantity
									}</Text>
              </Td>
              <Td>
                  <Text align="center" style="bold">${data.total_money}</Text>
              </Td>
          </Tr>
      </Table>
  </Receipt>
  </Receipts>`;
};
export default parseMoney;
