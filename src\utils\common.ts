import { Platform } from 'react-native';

declare global {
	interface Window {
		__TAURI__: any;
		isCustomer?: boolean;
	}
}

export const currencyFormat = (number: number | string | undefined): string => {
	if (number) {
		return number.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
	}
	return '0';
};

export const strSlug = (str: string): string => {
	str = str.replace(/^\s+|\s+$/g, '');
	str = str.toLowerCase();

	const from = 'ãàáäâẽèéëêìíïîõòóöôùúüûñç·/_,:;';
	const to = 'aaaaaeeeeeiiiiooooouuuunc------';
	for (let i = 0, l = from.length; i < l; i++) {
		str = str.replace(new RegExp(from.charAt(i), 'g'), to.charAt(i));
	}

	str = str
		.replace(/[^a-z0-9 -]/g, '')
		.replace(/\s+/g, '-')
		.replace(/-+/g, '-');

	return str;
};

export const getDate = (): string => {
	const today: Date = new Date();
	const day: number = today.getDate();
	const month: number = today.getMonth() + 1; // January is 0!
	const year: number = today.getFullYear();

	// Format the day and month with leading zeros if necessary
	const formattedDay: string = day < 10 ? '0' + day.toString() : day.toString();
	const formattedMonth: string =
		month < 10 ? '0' + month.toString() : month.toString();

	// Return the formatted date string
	return `${formattedDay}/${formattedMonth}/${year}`;
};

export const getDateTime = (): string => {
	const currentDate: Date = new Date();
	const hours: number = currentDate.getHours();
	const minutes: number = currentDate.getMinutes();
	const day: number = currentDate.getDate();
	const month: number = currentDate.getMonth() + 1;
	const year: number = currentDate.getFullYear();

	// Add leading zero if minutes is less than 10
	const formattedMinutes: string = minutes < 10 ? `0${minutes}` : `${minutes}`;

	return `${hours}:${formattedMinutes} ${day}/${month}/${year}`;
};

export const isCustomer = (): boolean => {
	if (Platform.OS === 'web' && typeof window !== 'undefined') {
		return typeof window.isCustomer !== 'undefined' && window.isCustomer;
	}
	return false;
};
