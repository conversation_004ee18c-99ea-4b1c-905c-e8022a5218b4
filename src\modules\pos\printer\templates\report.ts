import { IReport } from '../types';

const parseReport = async (data: IReport, printer: string) => {
	return `<Receipts>
  <Receipt name="Report" printer="${printer}" left="10">
      <Text size="12" style="bold" align="center">TOUCH CINEMA</Text>
      <Text style="bold" align="center">212 <PERSON><PERSON><PERSON>, P.Ph<PERSON>, TP.Pleiku, Gia Lai</Text>
      <Text size="8" align="center">www.touchcinema.com</Text>
      <Space height="8"/>
      <Text size="15" style="bold" align="center">KẾT CA</Text>
      <Space height="10"/>
      <Table template="5,8">
          <Tr>
              <Td>
                  <Text style="bold">Ngày:</Text>
              </Td>
              <Td>
                  <Text>${data.time}</Text>
              </Td>
          </Tr>
          <Tr>
              <Td>
                  <Text style="bold">Ca:</Text>
              </Td>
              <Td>
                  <Text>${data.shift}</Text>
              </Td>
          </Tr>
          <Tr>
              <Td>
                  <Text style="bold">Quầy:</Text>
              </Td>
              <Td>
                  <Text>${data.counter}</Text>
              </Td>
          </Tr>
          <Tr>
              <Td>
                  <Text style="bold">Nhân viên:</Text>
              </Td>
              <Td>
                  <Text>${data.seller}</Text>
              </Td>
          </Tr>
      </Table>
      <Space height="10"/>
      <Line type="dashed"/>
      <Space height="6"/>
      <Table template="1,1">
          <Tr>
              <Td>
                  <Text style="bold">Tổng tiền đã bán:</Text>
              </Td>
              <Td>
                  <Text style="bold" align="right">${data.sell}</Text>
              </Td>
          </Tr>
          <Tr>
              <Td>
                  <Text style="bold">Tổng tiền hủy (${data.cancel_count}):</Text>
              </Td>
              <Td>
                  <Text style="bold" align="right">${data.cancel}</Text>
              </Td>
          </Tr>
          <Tr>
              <Td>
                  <Text style="bold">Tiền vé (${data.ticket_count}):</Text>
              </Td>
              <Td>
                  <Text style="bold" align="right">${data.ticket}</Text>
              </Td>
          </Tr>
          <Tr>
              <Td>
                  <Text style="bold">Tiền F&#38;D (${data.combo_count}):</Text>
              </Td>
              <Td>
                  <Text style="bold" align="right">${data.combo}</Text>
              </Td>
          </Tr>
      </Table>
      <Space height="5"/>
      <Line type="dashed"/>
      <Space height="5"/>
      <Table template="1,1">
          <Tr>
              <Td>
                  <Text style="bold">Tổng doanh thu:</Text>
              </Td>
              <Td>
                  <Text style="bold" align="right">${data.total}</Text>
              </Td>
          </Tr>
      </Table>
      <Space height="5"/>
      <Line type="dashed"/>
      <Space height="5"/>
      <Table template="2,3">
          <Tr>
              <Td>
                  <Text style="bold">Tiền mặt:</Text>
              </Td>
              <Td>
                  <Text style="bold" align="right">${data.cash}</Text>
              </Td>
          </Tr>
          <Tr>
              <Td>
                  <Text style="bold">Quẹt thẻ:</Text>
              </Td>
              <Td>
                  <Text style="bold" align="right">${data.card}</Text>
              </Td>
          </Tr>
          <Tr>
              <Td>
                  <Text style="bold">MoMo:</Text>
              </Td>
              <Td>
                  <Text style="bold" align="right">${data.momo}</Text>
              </Td>
          </Tr>
          <Tr>
              <Td>
                  <Text style="bold">VnPay:</Text>
              </Td>
              <Td>
                  <Text style="bold" align="right">${data.vnpay}</Text>
              </Td>
          </Tr>
          <Tr>
              <Td>
                  <Text style="bold">ShopeePay:</Text>
              </Td>
              <Td>
                  <Text style="bold" align="right">${data.shopeepay}</Text>
              </Td>
          </Tr>
          <Tr>
              <Td>
                  <Text style="bold">Grab:</Text>
              </Td>
              <Td>
                  <Text style="bold" align="right">${data.grab}</Text>
              </Td>
          </Tr>
          <Tr>
              <Td>
                  <Text style="bold">Beamin:</Text>
              </Td>
              <Td>
                  <Text style="bold" align="right">${data.beamin}</Text>
              </Td>
          </Tr>
      </Table>
      <Space height="5"/>
      <Line type="dashed"/>
      <Space height="5"/>
      <Table template="2,3">
          <Tr>
              <Td>
                  <Text style="bold">Voucher (${data.voucher_count}):</Text>
              </Td>
              <Td>
                  <Text style="bold" align="right">${data.voucher}</Text>
              </Td>
          </Tr>
        </Table>
  </Receipt>
  </Receipts>`;
};
export default parseReport;
