/* These styles make the body full-height */
html,
body {
	height: 100%;
	user-select: none;
}
/* These styles disable body scrolling if you are using <ScrollView> */
body {
	overflow: hidden;
}
/* These styles make the root element full-height */
#root {
	display: flex;
	height: 100%;
}

#root iframe {
	height: 100%;
}

select {
	padding: 6px;
	border-color: #ddd;
}

.scroll-container.indiana-scroll-container {
	flex: 1;
	scroll-behavior: smooth;
}
.indiana-scroll-container.scroll {
	overflow-y: scroll;
}
.scroll-horizontal > div {
	width: max-content;
}
::-webkit-scrollbar {
	width: 7px;
	height: 5px;
}
::-webkit-scrollbar-corner {
	background: rgba(0, 0, 0, 0);
}
::-webkit-scrollbar-thumb {
	background-color: #ccc;
	border-radius: 2px;
	background-clip: content-box;
}
::-webkit-scrollbar-track {
	background-color: rgba(0, 0, 0, 0);
}
