import {
	WebviewWindow,
	availableMonitors,
	getCurrent,
} from '@tauri-apps/api/window';

import { isTauri } from '@/utils';

type Monitor = {
	size: { width: number; height: number };
	position: { x: number; y: number };
	scaleFactor: number;
};

export const getMonitors = async (): Promise<Monitor[]> => {
	if (isTauri) {
		const monitors = await availableMonitors();
		return monitors;
	}
	return [];
};

export const createWindow = async (
	url: string,
	label: string,
	monitor: Monitor,
): Promise<void> => {
	if (isTauri) {
		new WebviewWindow(label, {
			url,
			title: label.toUpperCase(),
			x: monitor.position.x,
			y: monitor.position.y,
			width: monitor.size.width,
			height: monitor.size.height,
			fullscreen: !__DEV__,
			skipTaskbar: !__DEV__,
			alwaysOnTop: !__DEV__,
		});
	}
};

export const initMonitor = async (): Promise<void> => {
	if (isTauri) {
		const monitors = await getMonitors();
		if (monitors.length > 1) {
			for (let monitor of monitors) {
				if (monitor.position.x !== 0) {
					await createWindow('/customer', 'customer', monitor);
					break;
				}
			}
		}
		const current = await getCurrent();
		if (current.label === 'customer') {
			document.title = 'Customer';
			window.isCustomer = true;
		}
	} else {
		if (!__DEV__ && window.location.pathname.search('/customer') === -1) {
			createIframe('/customer', 'customer');
		}
	}
};

const createIframe = (url: string, label: string) => {
	const customerWindow = window.open(url, label, 'fullscreen');
	if (customerWindow) {
		customerWindow.isCustomer = true;
	}
};
