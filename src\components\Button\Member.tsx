import React, { useCallback, useState } from 'react';
import {
	StyleSheet,
	Text,
	TouchableOpacity,
	View,
	ViewStyle,
	TextStyle,
} from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import isEqual from 'react-fast-compare';
import { router, useFocusEffect } from 'expo-router';

import {
	getMember,
	setMember,
	changePrices,
	getMemberId,
	setLoading,
	setMemberId,
} from '@/store';
import { memberService } from '@/services';
import { Modal, Button, Alert, TextInput } from '@/components';
import { primaryColor } from '@/constants';
import { showMessage } from '@/utils';

type Props = {
	text?: string;
	style: ViewStyle;
	textStyle?: TextStyle;
};

type Params = {
	id: string;
	phone: string;
	identification: string;
};

export const MemberButton = React.memo(
	({ text = 'KHTT', style, textStyle }: Props) => {
		const dispatch = useDispatch();

		const [modal, setModal] = useState(false);
		const [code, setCode] = useState('');

		const member = useSelector(getMember);
		const newMember = useSelector(getMemberId);

		useFocusEffect(
			useCallback(() => {
				if (newMember) {
					setCode(newMember);
					doQuery(newMember);
					dispatch(setMemberId(null));
				}
			}, [newMember]), // eslint-disable-line react-hooks/exhaustive-deps
		);

		const openModal = () => {
			setCode('');
			setModal(true);
		};

		const closeModal = () => {
			setModal(false);
		};

		const register = () => {
			setModal(false);
			router.navigate({
				pathname: '/cashier/members/create',
			});
		};

		const query = async () => {
			await doQuery(code);
		};

		const doQuery = async (code: string) => {
			if (code.length < 7) {
				return showMessage({
					message: 'Vui lòng nhập SĐT/Mã thanh viên',
					type: 'danger',
				});
			}
			dispatch(setLoading(true));
			const prefix = code.substring(0, 2).toUpperCase();
			const params: Params = {
				id: '',
				phone: '',
				identification: '',
			};
			if (prefix === 'TV' || prefix === 'TM' || prefix === 'TD') {
				params.id = code;
			} else if (code.length === 12 || code.length === 9) {
				params.identification = code;
			} else {
				params.phone = code;
			}
			try {
				const response = await memberService.search(params);
				if (response) {
					dispatch(setMember(response));
					dispatch(changePrices('Người lớn Member'));
					closeModal();
				} else {
					showMessage({
						message: 'SĐT hoặc mã thành viên không chính xác',
						type: 'danger',
					});
				}
			} catch (e) {
				console.error(e);
			}
			dispatch(setLoading(false));
		};

		const cancelMember = useCallback(() => {
			Alert.alert('KHTT', 'Bạn có chắc muốn bỏ chọn khách hàng này?', [
				{
					text: 'Không',
					style: 'cancel',
				},
				{ text: 'Bỏ chọn KH', onPress: onCancel },
			]);
		}, []); // eslint-disable-line react-hooks/exhaustive-deps

		const onCancel = () => {
			dispatch(changePrices('Người lớn'));
			dispatch(setMember(null));
		};

		return (
			<View style={[styles.btn, style]}>
				{member && member.member_id ? (
					<TouchableOpacity onPress={cancelMember} style={styles.btnWrap}>
						<Text style={textStyle} numberOfLines={1}>
							KH: {member.name}
						</Text>
					</TouchableOpacity>
				) : (
					<TouchableOpacity onPress={openModal} style={styles.btnWrap}>
						<Text style={textStyle}>{text}</Text>
					</TouchableOpacity>
				)}
				<Modal visible={modal} onRequestClose={closeModal} style={styles.modal}>
					<View style={styles.modalBody}>
						<View style={styles.inputWrap}>
							{modal && (
								<TextInput
									value={code}
									onChangeText={setCode}
									placeholder="Nhập mã KH/SĐT/CCCD"
									autoCapitalize="characters"
									autoFocus
									onSubmitEditing={query}
									clearButtonMode="always"
									style={styles.input}
								/>
							)}
						</View>
						<Button onPress={query} text="Xác nhận" style={styles.submit} />
						<Button
							onPress={register}
							text="Đăng ký Member"
							style={styles.register}
							textStyle={styles.registerLabel}
							background="transparent"
						/>
					</View>
				</Modal>
			</View>
		);
	},
	isEqual,
);

MemberButton.displayName = 'MemberButton';

const styles = StyleSheet.create({
	bg: {
		flex: 1,
	},
	modal: {},
	inputWrap: {
		paddingBottom: 30,
	},
	btn: {
		padding: 10,
		paddingHorizontal: 20,
		marginHorizontal: 10,
		borderRadius: 6,
		backgroundColor: primaryColor,
	},
	btnWrap: {
		alignItems: 'center',
		justifyContent: 'center',
		height: '100%',
		width: '100%',
	},
	modalBody: {
		backgroundColor: '#fff',
		padding: 40,
		paddingBottom: 20,
		minWidth: 350,
		minHeight: 200,
		borderRadius: 15,
		alignItems: 'center',
	},
	input: {
		padding: 10,
		width: 250,
		borderColor: '#ddd',
		borderWidth: 1,
	},
	submit: {
		width: 140,
		alignItems: 'center',
	},
	card: {
		margin: 10,
	},
	member: {
		backgroundColor: '#d6225d',
		padding: 20,
		paddingHorizontal: 50,
		borderTopLeftRadius: 40,
		borderBottomLeftRadius: 40,
		marginRight: -20,
		marginLeft: -50,
		elevation: 8,
		marginBottom: 25,
		marginTop: 20,
	},
	textMember: {
		color: '#fff',
		fontSize: 25,
		textAlign: 'center',
	},
	register: {
		textAlign: 'center',
		marginTop: 20,
	},
	registerLabel: {
		color: '#000',
		fontSize: 16,
	},
});
