import React from 'react';
import { StyleSheet, Text } from 'react-native';

type Props = {
	ticket: {
		time: string;
		movie_name_vn: string;
	};
};
const MovieName = React.memo(({ ticket }: Props) => {
	return (
		<Text style={styles.movie} numberOfLines={1}>
			{ticket.time} - {ticket.movie_name_vn}
		</Text>
	);
});

MovieName.displayName = 'MovieName';

const styles = StyleSheet.create({
	movie: {
		fontSize: 18,
		marginVertical: 5,
		color: 'red',
	},
});

export default MovieName;
