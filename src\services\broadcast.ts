import { Platform } from 'react-native';

import { isCustomer } from '@/utils';

const channelName = 'touchcinema-pos';

export const channel: any = new BroadcastChannel(channelName);

export interface IPayload {
	cinema?: number | string;
	action?: string;
	route?: string;
	payload?: any;
	scrollerPos?: boolean;
}

export const sendToCustomer = (payload: IPayload) => {
	if (Platform.OS === 'web' && !isCustomer()) {
		channel.postMessage(payload);
	}
};
