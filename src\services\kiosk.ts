import { QrCodeProps } from '@/store';

import { get } from './api';

const withQrCode = async ({ orderId, amount, gateway }: QrCodeProps) => {
	const response = await get(
		`https://kiosk.touchcinema.com/pos/${gateway}/${orderId}?amount=${amount}`,
	);
	if (response.qrcode) {
		return response.qrcode;
	}
	return false;
};

const queryQrOrder = async (
	orderId: number,
	gateway: string,
	amount: number = 0,
) => {
	const response = await get(
		`https://kiosk.touchcinema.com/pos/${gateway}/query/${orderId}?amount=${amount}`,
	);
	return response;
};

export const kioskService = {
	withQrCode,
	queryQrOrder,
};
