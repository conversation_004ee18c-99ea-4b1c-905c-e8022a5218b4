import { useState } from 'react';
import Icon from '@expo/vector-icons/Ionicons';

import { toggleFullScreen } from '@/modules/pos';

import MenuItem from './MenuItem';

const FullscreenButton = () => {
	const [isFull, setFull] = useState(false);

	const renderIcon = ({ color, size }: { color: string; size: number }) => (
		<Icon
			size={size}
			name={isFull ? 'contract-outline' : 'expand-outline'}
			color={color}
		/>
	);

	const onPress = () => {
		toggleFullScreen();
		setFull(!isFull);
	};

	return (
		<MenuItem
			icon={renderIcon}
			onPress={onPress}
			label={'Fullscreen'}
			isFocused={false}
		/>
	);
};

export default FullscreenButton;
