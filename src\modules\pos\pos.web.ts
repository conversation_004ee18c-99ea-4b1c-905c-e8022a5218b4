import { getVersion as getTauriVersion } from '@tauri-apps/api/app';
import {
	checkUpdate as tauriCheckUpdate,
	installUpdate,
} from '@tauri-apps/api/updater';
import { exit, relaunch } from '@tauri-apps/api/process';
import { Command } from '@tauri-apps/api/shell';
import { appWindow } from '@tauri-apps/api/window';

import { isTauri } from '@/utils';

export const getVersion = async (): Promise<string> => {
	if (isTauri) {
		const appVersion = await getTauriVersion();
		return appVersion;
	}

	return '';
};

export const checkUpdate = async (): Promise<boolean> => {
	if (isTauri) {
		const update = await tauriCheckUpdate();
		if (update.shouldUpdate) {
			return true;
		}
	}

	return false;
};

export const requestUpdate = async (): Promise<void> => {
	if (isTauri) {
		const update = await tauriCheckUpdate();
		if (update.shouldUpdate) {
			await installUpdate();
			await relaunch();
		}
	}
};

export const openCashDrawer = async (): Promise<void> => {
	if (isTauri) {
		const result = await new Command('tw-printer', [
			'drawer',
			'open',
		]).execute();
		console.log(result);
	}
};

export const toggleFullScreen = async (): Promise<void> => {
	if (isTauri) {
		const current = await appWindow.isFullscreen();
		await appWindow.setFullscreen(!current);
	} else {
		webToggleFullScreen();
	}
};

export const exitApp = async (): Promise<void> => {
	if (isTauri) {
		await exit(1);
	}
};

export const reLoad = async (): Promise<void> => {
	window.location.reload();
};

const webToggleFullScreen = (document: any = false): void => {
	const doc = document !== false ? document : window.document;
	const docEl = doc.documentElement;

	const requestFullScreen = docEl.requestFullscreen;
	const cancelFullScreen = doc.exitFullscreen;

	if (!doc.fullscreenElement) {
		requestFullScreen.call(docEl);
	} else {
		cancelFullScreen.call(doc);
	}
};
