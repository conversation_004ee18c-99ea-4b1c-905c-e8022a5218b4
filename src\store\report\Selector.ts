import { RootState } from '@/store';

// Money selectors
export const getMoneyValue = (name: number) => (state: RootState) =>
	state.report.moneyData.find((item) => item.name === name)?.quantity || 0;
export const getMoneyData = (state: RootState) => state.report.moneyData;
export const getMoneyTotal = (state: RootState) =>
	state.report.moneyData.reduce((total, item) => total + (item.total || 0), 0);
export const getMoneyTotalQuantity = (state: RootState) =>
	state.report.moneyData.reduce(
		(total, item) => total + (item.quantity || 0),
		0,
	);

// Report selectors
export const getShifts = (state: RootState) => state.report.shifts;
export const getFromDate = (state: RootState) => state.report.from;
export const getToDate = (state: RootState) => state.report.to;
export const getSelectedShift = (state: RootState) => state.report.shift;
export const getReportData = (state: RootState) => state.report.reportData;

export const getCanPrintProducts = (state: RootState) => {
	const reportData = state.report.reportData;
	return (
		reportData &&
		reportData.products &&
		reportData.products.success &&
		reportData.products.success.length > 0
	);
};
