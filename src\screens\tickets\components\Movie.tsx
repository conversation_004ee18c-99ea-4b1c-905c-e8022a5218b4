import React from 'react';
import { View, Text, StyleSheet, Image } from 'react-native';
import isEqual from 'react-fast-compare';

import Showtimes from './Showtimes';
import { IMovie } from '../types';

type MovieProps = {
	data: IMovie;
};

const Movie = React.memo(({ data }: MovieProps) => {
	return (
		<View style={styles.item}>
			<View style={styles.poster}>
				<Image source={{ uri: data.poster }} style={styles.posterSrc} />
				<View style={styles.attr}>
					<Text style={styles.duration}>{data.duration} phút</Text>
				</View>
				{data.age && (
					<View style={styles.ribbon}>
						<Text style={styles.ageIcon}>{data.age}</Text>
					</View>
				)}
			</View>
			<View style={styles.detail}>
				<View style={styles.movie}>
					<Text style={styles.movieName} numberOfLines={2}>
						{data.name}
					</Text>
				</View>
				<View style={styles.showtimes}>
					<Showtimes data={data} />
				</View>
			</View>
		</View>
	);
}, isEqual);

Movie.displayName = 'Movie';

const styles = StyleSheet.create({
	item: {
		flex: 1,
		flexDirection: 'row',
		marginHorizontal: 5,
		marginBottom: 15,
		position: 'relative',
		alignItems: 'flex-end',
	},
	poster: {
		position: 'absolute',
		left: 12,
		bottom: 0,
		width: 120,
		height: 180,
		borderRadius: 10,
		zIndex: 9,
		borderColor: '#ddd',
		borderWidth: 1,
	},
	posterSrc: {
		width: '100%',
		height: '100%',
		borderRadius: 10,
	},
	detail: {
		bottom: -10,
		width: '100%',
	},
	showtimes: {
		width: '100%',
		minHeight: 150,
		backgroundColor: '#fff',
		borderRadius: 5,
		padding: 5,
		paddingLeft: 135,
	},
	movie: {
		minHeight: 50,
		justifyContent: 'center',
	},
	movieName: {
		paddingLeft: 135,
		textTransform: 'uppercase',
		fontSize: 16,
		fontWeight: 'bold',
		color: '#000',
	},
	attr: {
		position: 'absolute',
		bottom: 0,
		left: 0,
		width: '100%',
		color: '#fff',
		alignItems: 'center',
	},
	ribbon: {
		position: 'absolute',
		backgroundColor: '#ffae00',
		top: 17,
		left: -4,
		color: '#fff',
		zIndex: 9,
		paddingVertical: 4,
		paddingHorizontal: 6,
		borderRadius: 2,
	},
	ageIcon: {
		color: '#000',
		fontSize: 20,
		fontWeight: 'bold',
		textTransform: 'uppercase',
		lineHeight: 20,
		textAlign: 'center',
	},
	duration: {
		color: '#fff',
		backgroundColor: 'rgba(0, 0, 0, .65)',
		width: '100%',
		textAlign: 'center',
		borderBottomLeftRadius: 10,
		borderBottomRightRadius: 10,
		paddingVertical: 2,
		fontSize: 15,
	},
	btn: {
		backgroundColor: '#fff',
		marginTop: 10,
		borderRadius: 10,
		height: 45,
		overflow: 'hidden',
	},
	btnLabel: {
		lineHeight: 45,
		textAlign: 'center',
		color: '#c41871',
		fontSize: 16,
	},
	btnActive: {
		backgroundColor: '#bf1659',
	},
	btnLabelActive: {
		color: '#fff',
	},
});

export default Movie;
