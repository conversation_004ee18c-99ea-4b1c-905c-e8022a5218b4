import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import axios from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';

import { reLoad } from '@/modules/pos';
import { sendToCustomer, authService } from '@/services';
import { isCustomer } from '@/utils';
import type { RootState } from '@/store';

export type LoginParams = {
	email: string;
	password: string;
};

export interface IConfig {
	server: string;
	printer: string;
	counter: string;
	group_combo: boolean;
}

export const loginAction = createAsyncThunk(
	'global/loginAction',
	async (args: LoginParams, thunkApi) => {
		thunkApi.dispatch(setLoading(true));
		try {
			const response = await authService.login(args);
			thunkApi.dispatch(setLoading(false));
			return response;
		} catch (e) {
			console.log(e);
		}
		thunkApi.dispatch(setLoading(false));
	},
);

export const getUserData = createAsyncThunk(
	'global/getUserData',
	async (token: string, thunkApi) => {
		thunkApi.dispatch(setLoading(true));
		try {
			axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
			const response = await authService.getUser();
			thunkApi.dispatch(setLoading(false));
			return { user: response.data, token: token };
		} catch (e) {
			console.log(e);
		}
		thunkApi.dispatch(setLoading(false));
	},
);

type AppState = {
	modalSuccess: boolean;
	isLoading: boolean;
	menu: string;
	message: string;
	user: any;
	token: string;
	configs: IConfig;
	member?: string;
};

const initialState: AppState = {
	modalSuccess: false,
	isLoading: false,
	menu: '/cashier',
	message: '',
	user: {},
	token: '',
	configs: {
		server: 'http://pleiku.touchcinema.com:8000/v1/',
		printer: '',
		counter: '',
		group_combo: true,
	},
};

export const AppSlice = createSlice({
	name: 'app',
	initialState,
	reducers: {
		setModalSuccess: (state, action) => {
			state.modalSuccess = action.payload;
		},
		setLoading: (state, action) => {
			if (!action.payload) {
				state.message = '';
			}
			state.isLoading = action.payload;
		},
		setMessage: (state, action) => {
			state.message = action.payload;
		},
		setToken: (state, action) => {
			axios.defaults.headers.common['Authorization'] =
				`Bearer ${action.payload.token}`;
			axios.defaults.baseURL = action.payload.api;
			state.token = action.payload.token;
		},
		setUser: (state, action) => {
			state.user = {
				...state.user,
				...action.payload,
			};
		},
		setConfigs: (state, action) => {
			state.configs = action.payload;
			axios.defaults.baseURL = action.payload.server;
			AsyncStorage.setItem('configs', JSON.stringify(action.payload));
		},
		setMenu: (state, action) => {
			state.menu = action.payload;
		},
		logout: (state) => {
			state.token = '';
			state.user = {};
			AsyncStorage.removeItem('token');
			reLoad();
		},
		setMemberId: (state, action) => {
			state.member = action.payload;
		},
	},
	extraReducers: (builder) => {
		builder.addCase(loginAction.fulfilled, (state, action) => {
			if (action.payload && action.payload.user) {
				state.user = action.payload.user;
				state.token = action.payload.token;
				axios.defaults.headers.common['Authorization'] =
					`Bearer ${action.payload.token}`;
				sendToCustomer({
					action: 'setToken',
					payload: {
						token: action.payload.token,
						api: state.configs.server,
					},
				});
				if (!isCustomer()) {
					AsyncStorage.setItem('token', action.payload.token);
				}
			}
		});
		builder.addCase(getUserData.fulfilled, (state, action) => {
			if (action.payload && action.payload.user) {
				state.user = action.payload.user;
				state.token = action.payload.token;
				sendToCustomer({
					action: 'setToken',
					payload: {
						token: action.payload.token,
						api: state.configs.server,
					},
				});
			}
		});
	},
});

export const {
	setModalSuccess,
	setLoading,
	setToken,
	setUser,
	setMessage,
	setConfigs,
	setMenu,
	logout,
	setMemberId,
} = AppSlice.actions;

export const isLoading = (state: RootState) => state.app.isLoading;
export const getMessage = (state: RootState) => state.app.message;
export const getToken = (state: RootState) => state.app.token;
export const getUser = (state: RootState) => state.app.user;
export const getConfigs = (state: RootState) => state.app.configs;
export const getCurrentMenu = (state: RootState) => state.app.menu;
export const isShowSuccess = (state: RootState) => state.app.modalSuccess;
export const getMemberId = (state: RootState) => state.app.member;

export const AppReducer = AppSlice.reducer;
