import React, { useCallback, useEffect, useState } from 'react';
import { StyleSheet, View } from 'react-native';
import { useSelector } from 'react-redux';
import { useFocusEffect } from 'expo-router';

import { useSocket } from '@/hooks';
import {
	currentShowtime,
	resetMap,
	getShowtime,
	useAppDispatch,
} from '@/store';
import { Configs } from '@/constants';

import { Maps, Sidebar, HeaderBar } from './components';
import { isCustomer } from '@/utils';

export const BookingScreen = () => {
	const dispatch = useAppDispatch();
	const socket = useSocket(Configs.socket_url);

	const showtime = useSelector(currentShowtime);

	const [ready, setReady] = useState<boolean>(false);

	const isCustomerScreen = isCustomer();

	useFocusEffect(
		useCallback(() => {
			if (!isCustomerScreen) {
				socket.connect();
				return () => {
					socket.close();
				};
			}
		}, [isCustomerScreen]), // eslint-disable-line react-hooks/exhaustive-deps
	);

	useEffect(() => {
		if (!ready) {
			setReady(true);
			if (showtime) {
				dispatch(getShowtime(showtime.id));
			}
		}
		return () => {
			dispatch(resetMap());
		};
	}, [showtime, ready]); // eslint-disable-line react-hooks/exhaustive-deps

	return (
		<View style={styles.bg}>
			<HeaderBar />
			<View style={styles.flex}>
				{ready && <Maps />}
				<View style={styles.sidebar}>
					<Sidebar />
				</View>
			</View>
		</View>
	);
};

const styles = StyleSheet.create({
	bg: {
		flex: 1,
	},
	flex: {
		flexDirection: 'row',
		flex: 1,
		justifyContent: 'flex-end',
	},
	sidebar: {
		width: 250,
		backgroundColor: '#fff',
		marginTop: -45,
		borderTopLeftRadius: 15,
		overflow: 'hidden',
		boxShadow: '0 0 10px rgba(0, 0, 0, .3)',
	},
});
