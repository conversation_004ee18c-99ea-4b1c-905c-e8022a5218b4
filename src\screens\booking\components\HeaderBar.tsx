import React, { useCallback } from 'react';
import { StyleSheet, Text } from 'react-native';
import { useSelector } from 'react-redux';
import { router } from 'expo-router';

import { bookingService } from '@/services';
import { Alert, Topbar } from '@/components';
import {
	useAppDispatch,
	getSelected,
	currentShowtime,
	setSeats,
	setMember,
} from '@/store';
import { primaryColor } from '@/constants';

export const HeaderBar = React.memo(() => {
	const dispatch = useAppDispatch();
	const current = useSelector(currentShowtime);
	const seats = useSelector(getSelected);

	const onCancel = useCallback(() => {
		if (seats.length > 0) {
			Alert.alert('Quay lại', '<PERSON><PERSON> ghế đang chọn, bạn có muốn giữ lại?', [
				{
					text: 'Không',
					onPress: backWithReset,
				},
				{
					text: 'Giữ ghế',
					onPress: () => {
						router.dismiss();
					},
				},
			]);
		} else {
			dispatch(setMember(null));
			router.dismiss();
		}
	}, [seats]); // eslint-disable-line react-hooks/exhaustive-deps

	const backWithReset = () => {
		bookingService.reset();
		dispatch(setSeats([]));
		dispatch(setMember(null));
		router.dismiss();
	};

	return (
		<Topbar text={current?.movie || 'Đặt vé'} goBack={onCancel}>
			{current && (
				<Text numberOfLines={1} style={styles.title}>
					Suất chiếu
					<Text style={styles.bold}> {current.time}</Text> ngày
					<Text style={styles.bold}> {current.date}</Text>
				</Text>
			)}
		</Topbar>
	);
});

HeaderBar.displayName = 'HeaderBar';

const styles = StyleSheet.create({
	bg: {
		backgroundColor: primaryColor,
		paddingLeft: 15,
		height: 60,
		paddingRight: 250,
		alignItems: 'center',
		justifyContent: 'center',
	},
	movie: {
		color: '#fff',
		fontSize: 20,
		fontWeight: 'bold',
		textAlign: 'center',
	},
	title: {
		color: '#fff',
		textAlign: 'center',
		fontSize: 16,
	},
	bold: {
		fontWeight: 'bold',
	},
});
