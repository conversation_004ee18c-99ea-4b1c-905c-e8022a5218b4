import React from 'react';
import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import isEqual from 'react-fast-compare';
import Icon from '@expo/vector-icons/Ionicons';
import { useDispatch, useSelector } from 'react-redux';

import { printService } from '@/services';
import { showMessage, transformTickets } from '@/utils';
import { Alert } from '@/components';
import { getConfigs, getUser, setLoading } from '@/store';
import { primaryColor } from '@/constants';
import { ICartTicket } from '@/models';
import { printTask } from '@/modules/pos';

type TicketProps = {
	ticket: ICartTicket;
	type: string;
};

const Ticket = React.memo(({ ticket, type }: TicketProps) => {
	const dispatch = useDispatch();
	const configs = useSelector(getConfigs);
	const user = useSelector(getUser);

	const onPress = () => {
		if (ticket.status === true) {
			if (user.permission.indexOf('reprint') >= 0) {
				Alert.alert('In vé', 'V<PERSON> này đã in, bạn có muốn in lại?', [
					{
						text: 'Không',
						style: 'cancel',
					},
					{
						text: 'In lại',
						onPress: onPrint,
					},
				]);
			} else {
				showMessage({
					message: 'Bạn không có quyền in lại, liên hệ quản lý!',
					type: 'danger',
				});
			}
		} else {
			onPrint();
		}
	};

	const onPrint = async () => {
		if (ticket.id === undefined) {
			showMessage({
				message: 'ID vé không hợp lệ',
				type: 'danger',
			});
			return;
		}
		dispatch(setLoading(true));
		try {
			const response = await printService.printTicket(ticket.id, type);
			await printTask(
				transformTickets({
					tickets: [response],
					seller: user.name,
				}),
				configs.printer,
				'ticket',
			);
			showMessage({
				message: 'In vé ' + ticket.seat_name + ' thành công',
				type: 'success',
			});
		} catch (e) {
			console.log(e);
		}
		dispatch(setLoading(false));
	};

	return (
		<View style={styles.ticket}>
			<View style={styles.seat}>
				<Text style={styles.seatName}>{ticket.seat_name}</Text>
				<Text>{ticket.ticket_id}</Text>
			</View>
			<View style={styles.showtime}>
				<Text>
					Phòng chiếu: <Text style={styles.bold}>{ticket.room_name}</Text>
				</Text>
				<Text style={styles.bold}>
					{ticket.time} {ticket.date}
				</Text>
			</View>
			<View style={styles.movie}>
				<Text numberOfLines={1} style={styles.bold}>
					{ticket.movie_name_vn}
				</Text>
				<Text>Ngày mua: {ticket.create_date}</Text>
			</View>
			<View style={styles.action}>
				<TouchableOpacity onPress={onPress}>
					<Icon
						name="print"
						size={30}
						color={ticket.status === true ? '#818181' : primaryColor}
					/>
				</TouchableOpacity>
			</View>
		</View>
	);
}, isEqual);

Ticket.displayName = 'PrintTicket';

const styles = StyleSheet.create({
	ticket: {
		flexDirection: 'row',
		paddingHorizontal: 15,
	},
	seat: {
		width: 80,
	},
	seatName: {
		fontWeight: 'bold',
		fontSize: 25,
		color: '#000',
	},
	movie: {
		flex: 1,
		justifyContent: 'center',
	},
	showtime: {
		width: 140,
		justifyContent: 'center',
	},
	action: {
		width: 50,
		alignItems: 'flex-end',
		justifyContent: 'center',
	},
	bold: {
		fontWeight: 'bold',
	},
});

export default Ticket;
