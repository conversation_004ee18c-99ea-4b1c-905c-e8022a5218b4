{"$schema": "../node_modules/@tauri-apps/cli/schema.json", "build": {"beforeBuildCommand": "npx expo export --platform web", "beforeDevCommand": "pnpm start", "devPath": "http://localhost:8081", "distDir": "../dist"}, "package": {"productName": "TouchCinema POS", "version": "../package.json"}, "tauri": {"allowlist": {"shell": {"all": true, "scope": [{"name": "tw-printer", "cmd": ".\\plugins\\TwPrinter.exe", "args": true}, {"name": "get-printers", "cmd": "powershell", "args": ["Get-Printer | ConvertTo-Json"]}]}, "process": {"all": true}, "window": {"all": true}, "globalShortcut": {"all": true}, "path": {"all": true}, "fs": {"all": true, "scope": ["$APPLOCALDATA/*"]}}, "bundle": {"active": true, "category": "Business", "copyright": "", "deb": {"depends": []}, "externalBin": [], "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"], "identifier": "com.touchcinema.pos", "longDescription": "", "macOS": {"entitlements": null, "exceptionDomain": "", "frameworks": [], "providerShortName": null, "signingIdentity": null}, "resources": ["logo-ticket.png", "plugins"], "shortDescription": "", "targets": "all", "windows": {"certificateThumbprint": null, "digestAlgorithm": "sha256", "timestampUrl": ""}}, "security": {"csp": null, "dangerousUseHttpScheme": true, "dangerousRemoteDomainIpcAccess": [{"domain": "new.touchcinema.com", "windows": ["main", "customer", "manager"]}]}, "updater": {"active": true, "endpoints": ["https://u.mrtaiw.dev/api/check-update?app-id=com.touchcinema.pos"], "dialog": false, "pubkey": "dW50cnVzdGVkIGNvbW1lbnQ6IG1pbmlzaWduIHB1YmxpYyBrZXk6IDVGNDM4NENDQjdCRDU5OTcKUldTWFdiMjN6SVJEWDU5bEZQUXZ5UUhEL3k3dnpmS2Q4ZWZYbzBtdnVGczY0TUFzSXFIR1NUN3EK"}, "windows": [{"fullscreen": true, "height": 768, "resizable": true, "title": "TouchCinema POS", "width": 1024}]}}