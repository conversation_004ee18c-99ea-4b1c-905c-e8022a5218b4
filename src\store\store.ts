import { configureStore } from '@reduxjs/toolkit';
import { useDispatch, useSelector, TypedUseSelectorHook } from 'react-redux';

import { BookingReducer } from './booking';
import { ProductsReducer } from './product';
import { CartReducer } from './cart';
import { MemberReducer } from './member';
import { AppReducer } from './app';
import { ReportReducer } from './report';

export const store = configureStore({
	reducer: {
		app: AppReducer,
		booking: BookingReducer,
		products: ProductsReducer,
		cart: CartReducer,
		member: MemberReducer,
		report: ReportReducer,
	},
	middleware: (getDefaultMiddleware) =>
		getDefaultMiddleware({
			immutableCheck: false,
		}),
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

// Export typed hooks
export const useAppDispatch: () => AppDispatch = useDispatch;
export const useAppSelector: TypedUseSelectorHook<RootState> = useSelector;
