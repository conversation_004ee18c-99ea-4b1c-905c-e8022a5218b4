import React from 'react';
import { StyleSheet, Text, View } from 'react-native';
import { useSelector } from 'react-redux';

import { RootState } from '@/store';
import PaymentMethod from './PaymentMethod';

const PaymentMethods = React.memo(() => {
	const payment = useSelector((state: RootState) => state.cart.payment);
	const paymentMethods = useSelector(
		(state: RootState) => state.cart.payment_methods,
	);

	return (
		<View style={styles.wrap}>
			<Text style={styles.text}>Phương thức thanh toán</Text>
			<View style={styles.methods}>
				{paymentMethods.map((item) => (
					<PaymentMethod key={item.id} data={item} active={payment} />
				))}
			</View>
		</View>
	);
});

PaymentMethods.displayName = 'PaymentMethods';

const styles = StyleSheet.create({
	wrap: {
		marginTop: 10,
	},
	methods: {
		flexDirection: 'row',
		flexWrap: 'wrap',
		alignItems: 'flex-start',
	},
	text: {
		color: '#000',
		fontSize: 16,
		fontWeight: 'bold',
		paddingHorizontal: 5,
	},
});

export default PaymentMethods;
