import React, { useEffect, useState } from 'react';
import { View, Platform, StyleSheet } from 'react-native';

import { get } from '@/services';
import { Image } from '@/components';

type Props = {
	uri: string;
};

export const CinemaScreen = React.memo(({ uri }: Props) => {
	const [image, setImage] = useState<string | null>('');

	useEffect(() => {
		const getData = async () => {
			try {
				const response = await get(uri + '?type=json');
				if (response.length === 1) {
					setImage(response[0].image);
				} else {
					setImage(null);
				}
			} catch (e) {
				console.log(e);
				setImage(null);
			}
		};
		getData();
	}, [uri]);

	if (typeof image === 'string') {
		return (
			<View style={styles.bg}>
				{image.length > 0 && (
					<Image
						source={{ uri: image }}
						style={styles.image}
						contentFit="fill"
					/>
				)}
			</View>
		);
	}
	return (
		<View style={styles.bg}>
			{Platform.OS === 'web' && (
				<iframe src={uri} className="iframe" frameBorder="0" />
			)}
		</View>
	);
});

CinemaScreen.displayName = 'CinemaScreen';

const styles = StyleSheet.create({
	bg: {
		flex: 1,
	},
	image: {
		width: '100%',
		height: '100%',
	},
});
