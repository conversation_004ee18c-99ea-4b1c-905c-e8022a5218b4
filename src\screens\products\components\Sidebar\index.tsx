import React, { memo } from 'react';
import { Text, View, TouchableOpacity, StyleSheet } from 'react-native';
import { useSelector } from 'react-redux';
import Icon from '@expo/vector-icons/Ionicons';

import { useAppDispatch, getProducts, setProducts } from '@/store';
import { Separator, BoxAction, FlatList } from '@/components';
import { primaryColor } from '@/constants';
import type { IProduct } from '@/models';

import SideBarItem from './SidebarItem';

export const Sidebar = memo(() => {
	const products = useSelector(getProducts);

	const dispatch = useAppDispatch();

	const reset = () => {
		dispatch(setProducts([]));
	};

	const renderItem = ({ item }: { item: IProduct }) => (
		<SideBarItem data={item} />
	);

	return (
		<View style={styles.container}>
			<View style={styles.header}>
				<View style={styles.headerTitle}>
					<Text style={styles.title}>ĐANG CHỌN ({products.length})</Text>
				</View>
				<TouchableOpacity style={styles.reset} onPress={reset}>
					<Icon name="trash" size={16} />
					<Text style={styles.resetText}>Chọn lại</Text>
				</TouchableOpacity>
			</View>
			<Separator />
			<FlatList
				data={products}
				keyExtractor={(item) => item.id}
				renderItem={renderItem}
				ItemSeparatorComponent={Separator}
			/>
			<BoxAction />
		</View>
	);
});
Sidebar.displayName = 'SideBarProducts';

const styles = StyleSheet.create({
	container: {
		paddingHorizontal: 12,
		height: '100%',
	},
	header: {
		flexDirection: 'row',
		height: 60,
		alignItems: 'center',
	},
	headerTitle: {
		flex: 1,
	},
	reset: {
		width: 90,
		flexDirection: 'row',
		alignItems: 'center',
	},
	resetText: {
		color: '#000',
		fontSize: 16,
	},
	title: {
		color: primaryColor,
		fontSize: 20,
	},
});
