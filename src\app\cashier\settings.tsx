import React, { useEffect, useState } from 'react';
import {
	StyleSheet,
	Text,
	TextInput,
	View,
	TouchableOpacity,
	Switch,
} from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { Picker } from '@react-native-picker/picker';

import { Button, Alert } from '@/components';
import { showMessage } from '@/utils';
import {
	checkUpdate,
	getVersion,
	requestUpdate,
	getPrinters,
} from '@/modules/pos';
import { getConfigs, setConfigs } from '@/store';

import { version as tauriVersion } from '@/../package.json';

const SettingsScreen = () => {
	const dispatch = useDispatch();

	const configs = useSelector(getConfigs);

	const [version, setVersion] = useState<string>(tauriVersion);
	const [printers, setPrinters] = useState<string[]>([]);
	const [printer, setPrinter] = useState(configs.printer);
	const [counter, setCounter] = useState(configs.counter);
	const [groupCombo, setGroupCombo] = useState<boolean>(configs.group_combo);

	useEffect(() => {
		init();
		check(false);
	}, []); // eslint-disable-line react-hooks/exhaustive-deps

	const init = async (): Promise<void> => {
		const resPrinter = await getPrinters();
		const resVersion: string = await getVersion();
		setPrinters(resPrinter);
		setVersion(resVersion);
	};

	const save = () => {
		dispatch(
			setConfigs({
				...configs,
				printer,
				counter,
				group_combo: groupCombo,
			}),
		);
		showMessage({
			type: 'success',
			message: 'Lưu thành công',
		});
	};

	const check = async (notify: boolean = true): Promise<void> => {
		if (notify) {
			showMessage({
				message: 'Đang kiểm tra bản cập nhật..',
			});
		}
		const shouldUpdate = await checkUpdate();
		if (shouldUpdate) {
			Alert.alert('Update', 'Có bản cập nhật mới, bạn có muốn cập nhật?', [
				{
					text: 'Không',
					style: 'cancel',
				},
				{ text: 'Cập nhật', onPress: onUpdate },
			]);
		} else {
			if (notify) {
				showMessage({
					type: 'success',
					message: 'Không có bản cập nhật',
				});
			}
		}
	};

	const onPress = () => {
		check(true);
	};

	const onUpdate = async (): Promise<void> => {
		showMessage({
			message:
				'Đang tải bản cập nhật, sẽ khởi động lại ứng dụng trong giây lát',
		});
		await requestUpdate();
	};

	return (
		<View style={styles.bg}>
			<View style={styles.form}>
				<Text>Quầy</Text>
				<TextInput
					value={counter}
					onChangeText={setCounter}
					style={styles.input}
				/>
			</View>
			<View style={styles.form}>
				<Text>Máy in</Text>
				<Picker
					selectedValue={printer}
					onValueChange={setPrinter}
					style={styles.picker}>
					<Picker.Item label="" value="" />
					{printers.map((item, index) => (
						<Picker.Item label={item} value={item} key={index} />
					))}
				</Picker>
			</View>
			<View style={[styles.form, styles.inline]}>
				<Text style={styles.switch}>Nhóm combo</Text>
				<Switch onValueChange={setGroupCombo} value={groupCombo} />
			</View>
			<Button
				text="Lưu"
				onPress={save}
				style={styles.btn}
				textStyle={styles.label}
			/>
			<View style={styles.version}>
				<TouchableOpacity onPress={onPress}>
					<Text>Phiên bản: {version}</Text>
				</TouchableOpacity>
			</View>
		</View>
	);
};

const styles = StyleSheet.create({
	bg: {
		flex: 1,
		padding: 40,
	},
	input: {
		backgroundColor: '#fff',
		borderColor: '#ddd',
		borderWidth: 1,
		paddingVertical: 6,
		paddingHorizontal: 10,
	},
	picker: {},
	form: {
		marginBottom: 15,
	},
	inline: {
		flexDirection: 'row',
	},
	switch: {
		paddingRight: 10,
	},
	btn: {
		borderRadius: 0,
		margin: 0,
		marginTop: 10,
		backgroundColor: 'blue',
		width: 150,
		alignItems: 'center',
		alignSelf: 'center',
	},
	label: {
		fontSize: 18,
		color: '#fff',
	},
	version: {
		position: 'absolute',
		bottom: 10,
		right: 10,
	},
});

export default SettingsScreen;
