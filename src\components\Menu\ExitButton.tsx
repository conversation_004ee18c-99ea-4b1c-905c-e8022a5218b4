import Icon from '@expo/vector-icons/Ionicons';

import { Alert } from '@/components';
import { exitApp } from '@/modules/pos';

import MenuItem from './MenuItem';

const ExitButton = () => {
	const renderIcon = ({ color, size }: { color: string; size: number }) => (
		<Icon size={size} name="power-outline" color={color} />
	);
	const onPress = () => {
		Alert.alert('Exit', 'Bạn có chắc muốn thoát?', [
			{
				text: 'Huỷ',
				style: 'cancel',
			},
			{ text: 'Thoát', onPress: exitApp },
		]);
	};

	return (
		<MenuItem
			icon={renderIcon}
			onPress={onPress}
			label={'Thoát'}
			isFocused={false}
		/>
	);
};

export default ExitButton;
