import { useEffect } from 'react';
import { StyleSheet, View } from 'react-native';
import { router, Stack } from 'expo-router';

import { channel, IPayload } from '@/services';
import {
	useAppDispatch,
	clearOrder,
	setAmount,
	setCart,
	setGifts,
	setMember,
	setMoney,
	setOrderPoint,
	setPaymentMethod,
	setPaymentMethods,
	setQrcode,
	setModalSuccess,
	setToken,
	setSeats,
	setShowtime,
	setProducts,
} from '@/store';
import { WelcomeScreen } from '@/screens';
import { TabRoute } from '@/models';

export default function CustomerLayout() {
	const dispatch = useAppDispatch();

	useEffect(() => {
		const handleMessage = (e: any) => {
			if (e.data.action) {
				handleEvent(e.data);
			} else if (typeof e.data.route != 'undefined') {
				handleNavigation(e.data);
			}
		};
		channel.addEventListener('message', handleMessage);

		return () => {
			channel.removeEventListener('message', handleMessage);
		};
	}, []); // eslint-disable-line react-hooks/exhaustive-deps

	const handleEvent = (data: IPayload): void => {
		switch (data.action) {
			case 'setCart':
				dispatch(setCart(data.payload));
				break;
			case 'setMember':
				dispatch(setMember(data.payload));
				break;
			case 'setMoney':
				dispatch(setMoney(data.payload));
				break;
			case 'setAmount':
				dispatch(setAmount(data.payload));
				break;
			case 'setPaymentMethods':
				dispatch(setPaymentMethods(data.payload));
				break;
			case 'setPayment':
				dispatch(setPaymentMethod(data.payload));
				break;
			case 'setOrderPoint':
				dispatch(setOrderPoint(data.payload));
				break;
			case 'setGifts':
				dispatch(setGifts(data.payload));
				break;
			case 'setQrcode':
				dispatch(setQrcode(data.payload));
				break;
			case 'clearOrder':
				dispatch(setQrcode(null));
				dispatch(clearOrder());
				break;
			case 'showSuccess':
				dispatch(setQrcode(null));
				dispatch(setModalSuccess(true));
				break;
			case 'setToken':
				dispatch(setToken(data.payload));
				break;
			case 'setShowtime':
				dispatch(setShowtime(data.payload));
				break;
			case 'setProducts':
				dispatch(setSeats(data.payload.seats));
				dispatch(setProducts(data.payload.products));
				break;
		}
	};

	const handleNavigation = (data: { route: string }): void => {
		switch (data.route) {
			case '':
				router.navigate('/customer/promotions');
				break;
			case '/booking-products':
			case '/products':
				router.navigate('/customer/products');
				break;
			case '/tickets':
			case '/booking':
			case '/checkout':
			case '/exchange':
			case '/members/create':
			case '/members/edit':
				const route = ('/customer/stack' + data.route) as TabRoute;
				router.replace(route);
				break;
			default:
				router.navigate('/customer/promotions');
		}
	};

	return (
		<View style={styles.bg}>
			<Stack
				screenOptions={{
					headerShown: false,
				}}
			/>
			<WelcomeScreen />
		</View>
	);
}

const styles = StyleSheet.create({
	bg: {
		flex: 1,
		flexDirection: 'row',
	},
});
