import { createSlice, PayloadAction } from '@reduxjs/toolkit';

import { IReport, IReportShift } from '@/models';
import { getDate } from '@/utils';

export interface IMoneyDenomination {
	name: number;
	quantity: number;
	total: number;
}

interface ReportState {
	// Money counting state
	moneyData: IMoneyDenomination[];
	moneyTotal: number;

	// Report state
	shifts: IReportShift[];
	from: string;
	to: string;
	shift: number;
	reportData: IReport | null;
}

const defaultMoneyData: IMoneyDenomination[] = [
	{ name: 500000, quantity: 0, total: 0 },
	{ name: 200000, quantity: 0, total: 0 },
	{ name: 100000, quantity: 0, total: 0 },
	{ name: 50000, quantity: 0, total: 0 },
	{ name: 20000, quantity: 0, total: 0 },
	{ name: 10000, quantity: 0, total: 0 },
	{ name: 5000, quantity: 0, total: 0 },
	{ name: 2000, quantity: 0, total: 0 },
	{ name: 1000, quantity: 0, total: 0 },
	{ name: 500, quantity: 0, total: 0 },
];

const initialState: ReportState = {
	moneyData: defaultMoneyData,
	moneyTotal: 0,
	shifts: [],
	from: getDate() + ' 06:00',
	to: getDate() + ' 23:59',
	shift: 3,
	reportData: null,
};

export const ReportSlice = createSlice({
	name: 'report',
	initialState,
	reducers: {
		// Money actions
		updateMoneyQuantity: (
			state,
			action: PayloadAction<{ name: number; quantity: number }>,
		) => {
			const { name, quantity } = action.payload;
			const index = state.moneyData.findIndex((item) => item.name === name);
			if (index >= 0 && index < state.moneyData.length) {
				state.moneyData[index].quantity = quantity;
				state.moneyData[index].total =
					quantity > 0 ? quantity * state.moneyData[index].name : 0;
			}
		},

		resetMoneyData: (state) => {
			state.moneyData = defaultMoneyData;
		},

		// Report actions
		setShifts: (state, action: PayloadAction<IReportShift[]>) => {
			state.shifts = action.payload;
		},

		setFromDate: (state, action: PayloadAction<string>) => {
			state.from = action.payload;
		},

		setToDate: (state, action: PayloadAction<string>) => {
			state.to = action.payload;
		},

		setShift: (state, action: PayloadAction<number>) => {
			state.shift = action.payload;
		},

		setReportData: (state, action: PayloadAction<IReport>) => {
			state.reportData = action.payload;
		},
	},
});

export const {
	updateMoneyQuantity,
	resetMoneyData,
	setShifts,
	setFromDate,
	setToDate,
	setShift,
	setReportData,
} = ReportSlice.actions;

export default ReportSlice.reducer;
