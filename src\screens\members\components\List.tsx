import React from 'react';
import { Text, View } from 'react-native';
import { useSelector } from 'react-redux';

import { FlatList } from '@/components';
import { getData } from '@/store';
import type { IMemberDetail } from '@/models';

import Row from './Row';
import Empty from './Empty';
import styles from '../styles';

export const List = React.memo(() => {
	const data = useSelector(getData);

	const renderItem = ({ item }: { item: IMemberDetail }) => <Row data={item} />;

	return (
		<View style={styles.bg}>
			<View style={styles.list}>
				<View style={[styles.row]}>
					<View style={[styles.cell, styles.id, styles.header]}>
						<Text>Mã KH</Text>
					</View>
					<View style={[styles.cell, styles.name, styles.header]}>
						<Text>Họ tên</Text>
					</View>
					<View style={[styles.cell, styles.phone, styles.header]}>
						<Text>SĐT</Text>
					</View>
					<View style={[styles.cell, styles.birthday, styles.header]}>
						<Text><PERSON><PERSON><PERSON> sinh</Text>
					</View>
					<View style={[styles.cell, styles.identification, styles.header]}>
						<Text>CCCD</Text>
					</View>
					<View style={[styles.cell, styles.point, styles.header]}>
						<Text>Đ.Đổi thưởng</Text>
					</View>
					<View style={[styles.cell, styles.point, styles.header]}>
						<Text>Đ.Thăng hạng</Text>
					</View>
					<View style={[styles.cell, styles.level, styles.header]}>
						<Text>Cấp độ</Text>
					</View>
					<View
						style={[
							styles.cell,
							styles.action,
							styles.header,
							styles.actionHeader,
						]}
					/>
				</View>
				<FlatList
					data={data}
					keyExtractor={(item) => item.id}
					renderItem={renderItem}
					ListEmptyComponent={Empty}
					showsHorizontalScrollIndicator={true}
				/>
			</View>
		</View>
	);
});

List.displayName = 'MemberList';
