import { StyleSheet } from 'react-native';

const styles = StyleSheet.create({
	overlay: {
		backgroundColor: 'rgba(0, 0, 0, .5)',
		flex: 1,
		justifyContent: 'center',
		alignItems: 'center',
	},
	modal: {
		width: 280,
		backgroundColor: '#fff',
		borderRadius: 10,
		overflow: 'hidden',
	},
	buttons: {
		borderTopColor: '#ddd',
		borderTopWidth: 0.5,
		flexDirection: 'row',
	},
	content: {
		paddingHorizontal: 10,
		paddingVertical: 15,
		justifyContent: 'center',
		alignItems: 'center',
	},
	message: {},
	title: {
		marginBottom: 5,
	},
	button: {
		flex: 1,
		borderLeftColor: '#ddd',
		borderLeftWidth: 0.5,
		marginLeft: -1,
	},
	buttonText: {
		textAlign: 'center',
		color: '#2888ee',
		fontSize: 16,
		paddingVertical: 8,
		paddingHorizontal: 5,
	},
	icon: {
		width: 50,
		height: 70,
		alignSelf: 'center',
		marginBottom: 10,
	},
});

export default styles;
